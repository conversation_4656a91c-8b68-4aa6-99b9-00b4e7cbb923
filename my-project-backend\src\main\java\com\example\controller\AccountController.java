package com.example.controller;

import com.example.entity.RestBean;
import com.example.entity.dto.Account;
import com.example.entity.dto.AccountDetails;
import com.example.entity.vo.request.ChangePasswordVO;
import com.example.entity.vo.request.DetailsSaveVO;
import com.example.entity.vo.request.ModifyEmailVO;
import com.example.entity.vo.request.PrivacySaveVO;
import com.example.entity.vo.response.AccountDetailsVO;
import com.example.entity.vo.response.AccountPrivacyVO;
import com.example.entity.vo.response.AccountVO;
import com.example.service.AccountDetailsService;
import com.example.service.AccountPrivacyService;
import com.example.service.AccountService;
import com.example.utils.Const;
import com.example.utils.ControllerUtils;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 用户账户控制器
 * 处理普通用户相关的所有操作，包括：
 * - 获取用户基本信息
 * - 获取和修改用户详细资料
 * - 修改邮箱
 * - 修改密码
 * - 隐私设置管理
 */
@RestController
@RequestMapping("/api/user")
public class AccountController {

    /**
     * 用户账号服务
     */
    @Resource
    AccountService service;

    /**
     * 用户详情服务
     */
    @Resource
    AccountDetailsService detailsService;

    /**
     * 用户隐私设置服务
     */
    @Resource
    AccountPrivacyService privacyService;

    /**
     * 控制器工具类，用于处理消息响应
     */
    @Resource
    ControllerUtils utils;

    /**
     * 获取当前用户的基本信息
     * 
     * @param id 用户ID（从请求属性中获取）
     * @return 包含用户基本信息的AccountVO对象
     */
    @GetMapping("/info")
    public RestBean<AccountVO> info(@RequestAttribute(Const.ATTR_USER_ID) int id){
        // 根据ID查找用户
        Account account = service.findAccountById(id);
        // 转换为视图对象并返回
        return RestBean.success(account.asViewObject(AccountVO.class));
    }

    /**
     * 获取当前用户的详细资料
     * 如果用户详细资料不存在，则返回一个空的详细资料对象
     * 
     * @param id 用户ID（从请求属性中获取）
     * @return 包含用户详细资料的AccountDetailsVO对象
     */
    @GetMapping("/details")
    public RestBean<AccountDetailsVO> details(@RequestAttribute(Const.ATTR_USER_ID) int id){
        // 查找用户详细资料，如果不存在则创建一个新的
        AccountDetails details = Optional
                .ofNullable(detailsService.findAccountDetailsById(id))
                .orElseGet(AccountDetails::new);
        // 转换为视图对象并返回
        return RestBean.success(details.asViewObject(AccountDetailsVO.class));
    }

    /**
     * 保存用户详细资料
     * 检查用户名是否已被其他用户使用
     * 
     * @param id 用户ID（从请求属性中获取）
     * @param vo 包含详细资料的请求对象
     * @return 操作结果
     */
    @PostMapping("/save-details")
    public RestBean<Void> saveDetails(@RequestAttribute(Const.ATTR_USER_ID) int id,
                                      @RequestBody @Valid DetailsSaveVO vo){
        // 保存用户详细资料，并获取操作结果
        boolean success = detailsService.saveAccountDetails(id, vo);
        // 根据操作结果返回相应的响应
        return success ? RestBean.success() : RestBean.failure(400, "此用户名已被其他用户使用，请重新更换！");
    }

    /**
     * 修改用户邮箱
     * 需要提供验证码进行验证
     * 
     * @param id 用户ID（从请求属性中获取）
     * @param vo 包含新邮箱和验证码的请求对象
     * @return 操作结果
     */
    @PostMapping("/modify-email")
    public RestBean<Void> modifyEmail(@RequestAttribute(Const.ATTR_USER_ID) int id,
                                      @RequestBody @Valid ModifyEmailVO vo){
        // 使用工具类处理消息响应
        return utils.messageHandle(() -> service.modifyEmail(id, vo));
    }

    /**
     * 修改用户密码
     * 需要提供原密码进行验证
     * 
     * @param id 用户ID（从请求属性中获取）
     * @param vo 包含原密码和新密码的请求对象
     * @return 操作结果
     */
    @PostMapping("/change-password")
    public RestBean<Void> changePassword(@RequestAttribute(Const.ATTR_USER_ID) int id,
                                         @RequestBody @Valid ChangePasswordVO vo){
        // 使用工具类处理消息响应
        return utils.messageHandle(() -> service.changePassword(id, vo));
    }

    /**
     * 保存用户隐私设置
     * 控制哪些个人信息可以被其他用户查看
     * 
     * @param id 用户ID（从请求属性中获取）
     * @param vo 包含隐私设置的请求对象
     * @return 操作结果
     */
    @PostMapping("/save-privacy")
    public RestBean<Void> savePrivacy(@RequestAttribute(Const.ATTR_USER_ID) int id,
                                      @RequestBody @Valid PrivacySaveVO vo){
        // 保存用户隐私设置
        privacyService.savePrivacy(id, vo);
        return RestBean.success();
    }

    /**
     * 获取当前用户的隐私设置
     * 
     * @param id 用户ID（从请求属性中获取）
     * @return 包含隐私设置的AccountPrivacyVO对象
     */
    @GetMapping("/privacy")
    public RestBean<AccountPrivacyVO> privacy(@RequestAttribute(Const.ATTR_USER_ID) int id){
        // 获取用户隐私设置，转换为视图对象并返回
        return RestBean.success(privacyService.accountPrivacy(id).asViewObject(AccountPrivacyVO.class));
    }


}
