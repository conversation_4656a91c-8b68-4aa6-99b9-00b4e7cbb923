[ ] NAME:Current Task List DESCRIPTION:Root task for conversation 19b44ecf-f137-475b-9e5d-f4c165c9e397
-[x] NAME:设计AutoGen工作流架构 DESCRIPTION:定义三个agent的角色、系统提示词和工作流程，包括消息传递机制和错误处理策略
-[x] NAME:实现CodeWriter Agent DESCRIPTION:创建负责编写代码的agent，包含明确的角色定义和系统提示词
-[x] NAME:实现CodeReviewer Agent DESCRIPTION:创建负责审查代码并提出修改建议的agent
-[x] NAME:实现CodeOptimizer Agent DESCRIPTION:创建负责根据原代码和审查建议优化代码的agent
-[x] NAME:实现工作流管理器 DESCRIPTION:创建管理三个agent协作的工作流管理器，包含消息传递和状态管理
-[x] NAME:添加错误处理和日志 DESCRIPTION:实现完整的错误处理机制和详细的执行日志输出
-[x] NAME:创建使用示例和测试用例 DESCRIPTION:编写完整的使用示例和测试用例来验证工作流功能
-[x] NAME:编写详细文档 DESCRIPTION:创建包含代码注释和使用说明的完整文档