package com.example.controller;

import com.example.entity.RestBean;
import com.example.entity.dto.Account;
import com.example.entity.dto.Interact;
import com.example.entity.vo.request.AddCommentVO;
import com.example.entity.vo.request.TopicCreateVO;
import com.example.entity.vo.request.TopicUpdateVO;
import com.example.entity.vo.response.*;
import com.example.service.AccountService;
import com.example.service.TopicService;
import com.example.service.WeatherService;
import com.example.utils.Const;
import com.example.utils.ControllerUtils;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Pattern;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 论坛相关控制器
 * 处理论坛相关的所有操作，包括：
 * - 获取天气信息
 * - 获取话题类型列表
 * - 创建、更新、查询话题
 * - 互动操作（点赞、收藏）
 * - 评论管理
 */
@RestController
@RequestMapping("/api/forum")
public class ForumController {

    /**
     * 天气服务
     */
    @Resource
    WeatherService service;

    /**
     * 话题服务
     */
    @Resource
    TopicService topicService;

    /**
     * 控制器工具类
     */
    @Resource
    ControllerUtils utils;
    
    /**
     * 账户服务
     */
    @Resource
    AccountService accountService;

    /**
     * 获取指定地理位置的天气信息
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @return 天气信息VO对象
     */
    @GetMapping("/weather")
    public RestBean<WeatherVO> weather(double longitude, double latitude){
        WeatherVO vo = service.fetchWeather(longitude, latitude);
        return vo == null ?
                RestBean.failure(400, "获取地理位置信息与天气失败，请联系管理员！") : RestBean.success(vo);
    }

    /**
     * 获取所有话题类型列表
     * 
     * @return 话题类型VO对象列表
     */
    @GetMapping("/types")
    public RestBean<List<TopicTypeVO>> listTypes(){
        return RestBean.success(topicService
                .listTypes()
                .stream()
                .map(type -> type.asViewObject(TopicTypeVO.class))
                .toList());
    }

    /**
     * 创建新话题
     * 检查用户是否被禁言，如果被禁言则无法发帖
     * 
     * @param vo 话题创建VO对象
     * @param id 用户ID（从请求属性中获取）
     * @return 操作结果
     */
    @PostMapping("/create-topic")
    public RestBean<Void> createTopic(@Valid @RequestBody TopicCreateVO vo,
                                      @RequestAttribute(Const.ATTR_USER_ID) int id) {
        Account accountById = accountService.findAccountById(id);
        if(accountById.isMute()){
            return RestBean.failure(403, "你已被封禁，无法发帖！");
        }

        return utils.messageHandle(() -> topicService.createTopic(id, vo));
    }

    /**
     * 分页获取话题列表
     * 
     * @param page 页码（从0开始）
     * @param type 话题类型ID，0表示获取所有类型
     * @return 话题预览VO对象列表
     */
    @GetMapping("/list-topic")
    public RestBean<List<TopicPreviewVO>> listTopic(@RequestParam @Min(0) int page,
                                                    @RequestParam @Min(0) int type) {
        return RestBean.success(topicService.listTopicByPage(page + 1, type));
    }

    /**
     * 获取置顶话题列表
     * 
     * @return 置顶话题VO对象列表
     */
    @GetMapping("/top-topic")
    public RestBean<List<TopicTopVO>> topTopic(){
        return RestBean.success(topicService.listTopTopics());
    }

    /**
     * 获取指定话题的详细信息
     * 
     * @param tid 话题ID
     * @param id 用户ID（从请求属性中获取）
     * @return 话题详情VO对象
     */
    @GetMapping("/topic")
    public RestBean<TopicDetailVO> topic(@RequestParam @Min(0) int tid,
                                         @RequestAttribute(Const.ATTR_USER_ID) int id){
        return RestBean.success(topicService.getTopic(tid, id));
    }

    /**
     * 与话题互动
     * 包括点赞和收藏操作
     * 
     * @param tid 话题ID
     * @param type 互动类型（like或collect）
     * @param state 互动状态（true为添加互动，false为取消互动）
     * @param id 用户ID（从请求属性中获取）
     * @return 操作结果
     */
    @GetMapping("/interact")
    public RestBean<Void> interact(@RequestParam @Min(0) int tid,
                                   @RequestParam @Pattern(regexp = "(like|collect)") String type,
                                   @RequestParam boolean state,
                                   @RequestAttribute(Const.ATTR_USER_ID) int id) {
        topicService.interact(new Interact(tid, id, new Date(), type), state);
        return RestBean.success();
    }

    /**
     * 获取用户收藏的话题列表
     * 
     * @param id 用户ID（从请求属性中获取）
     * @return 话题预览VO对象列表
     */
    @GetMapping("/collects")
    public RestBean<List<TopicPreviewVO>> collects(@RequestAttribute(Const.ATTR_USER_ID) int id){
        return RestBean.success(topicService.listTopicCollects(id));
    }

    /**
     * 更新话题
     * 
     * @param vo 话题更新VO对象
     * @param id 用户ID（从请求属性中获取）
     * @return 操作结果
     */
    @PostMapping("/update-topic")
    public RestBean<Void> updateTopic(@Valid @RequestBody TopicUpdateVO vo,
                                      @RequestAttribute(Const.ATTR_USER_ID) int id){
        return utils.messageHandle(() -> topicService.updateTopic(id, vo));
    }

    /**
     * 添加评论
     * 检查用户是否被禁言，如果被禁言则无法评论
     * 
     * @param vo 评论添加VO对象
     * @param id 用户ID（从请求属性中获取）
     * @return 操作结果
     */
    @PostMapping("/add-comment")
    public RestBean<Void> addComment(@Valid @RequestBody AddCommentVO vo,
                                     @RequestAttribute(Const.ATTR_USER_ID) int id){
        Account accountById = accountService.findAccountById(id);
        if(accountById.isMute()){
            return RestBean.failure(403, "你已被封禁，无法创建回复！");
        }
        return utils.messageHandle(() -> topicService.createComment(id, vo));
    }

    /**
     * 分页获取话题的评论列表
     * 
     * @param tid 话题ID
     * @param page 页码（从0开始）
     * @return 评论VO对象列表
     */
    @GetMapping("/comments")
    public RestBean<List<CommentVO>> comments(@RequestParam @Min(0) int tid,
                                              @RequestParam @Min(0) int page){
        return RestBean.success(topicService.comments(tid, page + 1));
    }

    /**
     * 删除评论
     * 
     * @param id 评论ID
     * @param uid 用户ID（从请求属性中获取）
     * @return 操作结果
     */
    @GetMapping("/delete-comment")
    public RestBean<Void> deleteComment(@RequestParam @Min(0) int id,
                                        @RequestAttribute(Const.ATTR_USER_ID) int uid){
        topicService.deleteComment(id, uid);
        return RestBean.success();
    }
}
