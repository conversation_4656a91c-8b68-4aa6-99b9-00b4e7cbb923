/* 重置样式和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background-color: #ffffff;
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.top-nav {
    background-color: #fff;
    border-bottom: 1px solid #e6e6e6;
    padding: 0 24px;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 55px;
}

.nav-left, .nav-right {
    display: flex;
    align-items: center;
    gap: 24px;
}

.nav-link {
    color: #333;
    text-decoration: none;
    font-size: 13px;
    padding: 8px 0;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: #315efb;
}

.more-link {
    position: relative;
}

.login-btn {
    background-color: #4e6ef2;
    color: white !important;
    padding: 8px 16px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.login-btn:hover {
    background-color: #315efb;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* 百度Logo */
.logo-container {
    margin-bottom: 40px;
    text-align: center;
}

.logo {
    width: 270px;
    height: auto;
    max-width: 100%;
}

.logo svg {
    width: 100%;
    height: auto;
}

/* 搜索区域 */
.search-container {
    position: relative;
    width: 100%;
    max-width: 641px;
    margin-bottom: 30px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    border: 2px solid #c4c7ce;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
}

.search-box:hover {
    box-shadow: 0 2px 8px 1px rgba(64,60,67,.24);
}

.search-box:focus-within {
    border-color: #4285f4;
    box-shadow: 0 2px 8px 1px rgba(66,133,244,.28);
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 16px 20px;
    font-size: 16px;
    background: transparent;
    color: #333;
}

.search-input::placeholder {
    color: #9aa0a6;
}

.search-btn {
    background: none;
    border: none;
    padding: 16px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9aa0a6;
    transition: color 0.2s ease;
}

.search-btn:hover {
    color: #333;
}

.search-icon {
    width: 20px;
    height: 20px;
}

/* 搜索建议 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e6e6e6;
    border-top: none;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.suggestion-item {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* 搜索按钮组 */
.button-group {
    display: flex;
    gap: 16px;
    margin-bottom: 40px;
}

.search-button {
    background-color: #f8f9fa;
    border: 1px solid #f8f9fa;
    border-radius: 4px;
    color: #3c4043;
    font-size: 14px;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 54px;
    text-align: center;
}

.search-button:hover {
    box-shadow: 0 1px 1px rgba(0,0,0,.1);
    background-color: #f1f3f4;
    border: 1px solid #dadce0;
    color: #202124;
}

.search-button.primary {
    background-color: #4285f4;
    border: 1px solid #4285f4;
    color: white;
}

.search-button.primary:hover {
    background-color: #3367d6;
    border: 1px solid #3367d6;
    box-shadow: 0 1px 1px rgba(0,0,0,.1);
}

/* 快捷导航 */
.quick-nav {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 20px;
    margin-bottom: 40px;
    max-width: 500px;
    width: 100%;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #333;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.quick-link:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

.quick-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.quick-link span {
    font-size: 12px;
    color: #666;
}

/* 底部信息 */
.footer {
    background-color: #f2f2f2;
    padding: 20px;
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.footer-links {
    margin-bottom: 16px;
}

.footer-link {
    color: #666;
    text-decoration: none;
    font-size: 12px;
    margin: 0 8px;
    transition: color 0.2s ease;
}

.footer-link:hover {
    color: #315efb;
}

.footer-info {
    color: #999;
    font-size: 11px;
    line-height: 1.4;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-suggestions.show {
    display: block;
    animation: fadeIn 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 16px;
        height: 48px;
    }
    
    .nav-left, .nav-right {
        gap: 16px;
    }
    
    .nav-link {
        font-size: 12px;
    }
    
    .main-content {
        padding: 20px 16px;
    }
    
    .logo {
        width: 200px;
    }
    
    .search-container {
        max-width: 100%;
    }
    
    .search-input {
        padding: 14px 16px;
        font-size: 16px;
    }
    
    .search-btn {
        padding: 14px 16px;
    }
    
    .button-group {
        flex-direction: column;
        gap: 12px;
        width: 100%;
        max-width: 300px;
    }
    
    .search-button {
        width: 100%;
        padding: 12px 20px;
    }
    
    .quick-nav {
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
        max-width: 300px;
    }
    
    .quick-link {
        padding: 8px;
    }
    
    .quick-icon {
        font-size: 20px;
        margin-bottom: 4px;
    }
    
    .footer-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .top-nav {
        padding: 0 12px;
    }
    
    .nav-left {
        display: none;
    }
    
    .logo {
        width: 150px;
    }
    
    .search-input {
        padding: 12px 14px;
        font-size: 14px;
    }
    
    .search-btn {
        padding: 12px 14px;
    }
    
    .quick-nav {
        grid-template-columns: repeat(2, 1fr);
        max-width: 200px;
    }
}
