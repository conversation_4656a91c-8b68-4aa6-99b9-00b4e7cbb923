package com.example.controller;

import com.alibaba.fastjson2.JSONObject;
import com.example.entity.RestBean;
import com.example.entity.dto.Account;
import com.example.entity.dto.TopicType;
import com.example.entity.vo.request.AddCommentVO;
import com.example.entity.vo.request.TopicCreateVO;
import com.example.entity.vo.request.TopicUpdateVO;
import com.example.entity.vo.response.TopicPreviewVO;
import com.example.entity.vo.response.TopicTypeVO;
import com.example.service.TopicService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ForumControllerTest {

    @Mock
    private TopicService topicService;

    @Mock
    private Authentication authentication;

    @Mock
    private SecurityContext securityContext;

    @InjectMocks
    private ForumController forumController;

    private Account testAccount;
    private TopicType testType;

    @BeforeEach
    void setUp() {
        testAccount = new Account();
        testAccount.setId(1);
        testAccount.setUsername("testuser");

        testType = new TopicType();
        testType.setId(1);
        testType.setName("测试类型");
        testType.setColor("#FF5722");
        
        // 设置安全上下文
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        when(authentication.getPrincipal()).thenReturn(testAccount);
    }

    @Test
    void testListTypes() {
        // 准备测试数据
        List<TopicType> types = Arrays.asList(testType);
        when(topicService.listTypes()).thenReturn(types);

        // 执行测试
        RestBean<List<TopicTypeVO>> result = forumController.listTypes();

        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(1, result.getData().size());
        assertEquals("测试类型", result.getData().get(0).getName());
    }

    @Test
    void testCreateTopic_Success() {
        // 准备测试数据
        TopicCreateVO createVO = new TopicCreateVO();
        createVO.setTitle("测试话题");
        createVO.setContent(new JSONObject());
        createVO.setType(1);

        when(topicService.createTopic(anyInt(), any(TopicCreateVO.class))).thenReturn(null);

        // 执行测试
        RestBean<Void> result = forumController.createTopic(createVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(topicService).createTopic(eq(1), eq(createVO));
    }

    @Test
    void testCreateTopic_Failure() {
        // 准备测试数据
        TopicCreateVO createVO = new TopicCreateVO();
        createVO.setTitle("测试话题");
        createVO.setContent(new JSONObject());
        createVO.setType(1);

        when(topicService.createTopic(anyInt(), any(TopicCreateVO.class))).thenReturn("创建失败");

        // 执行测试
        RestBean<Void> result = forumController.createTopic(createVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("创建失败", result.getMessage());
    }

    @Test
    void testUpdateTopic_Success() {
        // 准备测试数据
        TopicUpdateVO updateVO = new TopicUpdateVO();
        updateVO.setId(1);
        updateVO.setTitle("更新的话题");
        updateVO.setContent(new JSONObject());
        updateVO.setType(1);

        when(topicService.updateTopic(anyInt(), any(TopicUpdateVO.class))).thenReturn(null);

        // 执行测试
        RestBean<Void> result = forumController.updateTopic(updateVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(topicService).updateTopic(eq(1), eq(updateVO));
    }

    @Test
    void testUpdateTopic_Failure() {
        // 准备测试数据
        TopicUpdateVO updateVO = new TopicUpdateVO();
        updateVO.setId(1);
        updateVO.setTitle("更新的话题");
        updateVO.setContent(new JSONObject());
        updateVO.setType(1);

        when(topicService.updateTopic(anyInt(), any(TopicUpdateVO.class))).thenReturn("更新失败");

        // 执行测试
        RestBean<Void> result = forumController.updateTopic(updateVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("更新失败", result.getMessage());
    }

    @Test
    void testAddComment_Success() {
        // 准备测试数据
        AddCommentVO commentVO = new AddCommentVO();
        commentVO.setTid(1);
        commentVO.setContent("测试评论");

        when(topicService.createComment(anyInt(), any(AddCommentVO.class))).thenReturn(null);

        // 执行测试
        RestBean<Void> result = forumController.addComment(commentVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(topicService).createComment(eq(1), eq(commentVO));
    }

    @Test
    void testAddComment_Failure() {
        // 准备测试数据
        AddCommentVO commentVO = new AddCommentVO();
        commentVO.setTid(1);
        commentVO.setContent("测试评论");

        when(topicService.createComment(anyInt(), any(AddCommentVO.class))).thenReturn("评论失败");

        // 执行测试
        RestBean<Void> result = forumController.addComment(commentVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("评论失败", result.getMessage());
    }
} 