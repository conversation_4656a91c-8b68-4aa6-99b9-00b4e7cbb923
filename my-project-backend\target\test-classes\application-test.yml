spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
  h2:
    console:
      enabled: true
  sql:
    init:
      mode: embedded
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto
  mapper-locations: classpath:mapper/*.xml

minio:
  endpoint: http://localhost:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucket: forum-test

spring.web:
  verify:
    mail-limit: 60
  flow:
    period: 3600
    limit: 20
    block: 1800
  cors:
    origin: '*'
    credentials: false
    methods: '*'

jwt:
  secret: abcdefghijklmnopqrstuvwxyz
  expire: 7 