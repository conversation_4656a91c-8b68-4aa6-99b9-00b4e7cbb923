package com.example.config;

import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ消息队列配置类
 * 负责配置和初始化消息队列，用于异步处理邮件发送等任务
 * 通过消息队列可以将耗时的操作异步处理，提高系统响应速度
 */
@Configuration
public class RabbitConfiguration {
    
    /**
     * 创建邮件队列Bean
     * 配置一个名为"mail"的持久化队列，用于处理邮件发送任务
     * 持久化队列可在RabbitMQ服务重启后仍然保留队列中的消息
     * 
     * @return 配置好的队列实例
     */
    @Bean("mailQueue")
    public Queue queue(){
        return QueueBuilder
                .durable("mail")  // 创建名为"mail"的持久化队列
                .build();
    }
}
