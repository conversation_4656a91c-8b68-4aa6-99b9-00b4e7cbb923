package com.example.controller;

import com.example.entity.RestBean;
import com.example.entity.vo.request.ConfirmResetVO;
import com.example.entity.vo.request.EmailRegisterVO;
import com.example.entity.vo.request.EmailResetVO;
import com.example.service.AccountService;
import com.example.utils.ControllerUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 认证授权控制器
 * 用于处理用户认证相关的所有操作，包括：
 * - 注册新用户
 * - 获取验证码
 * - 重置密码
 * - 验证邮箱
 * 
 * 所有接口均无需登录即可访问
 */
@Validated
@RestController
@RequestMapping("/api/auth")
@Tag(name = "登录校验相关", description = "包括用户登录、注册、验证码请求等操作。")
public class AuthorizeController {

    /**
     * 用户账号服务
     */
    @Resource
    AccountService accountService;

    /**
     * 控制器工具类，用于处理消息响应
     */
    @Resource
    ControllerUtils utils;

    /**
     * 请求邮件验证码
     * 用于注册、重置密码或修改邮箱时获取验证码
     * 
     * @param email 目标邮箱地址
     * @param type 验证码类型，可选值：register(注册)、reset(重置密码)、modify(修改邮箱)
     * @param request HTTP请求对象，用于获取请求IP地址
     * @return 操作结果，如果频率限制或邮件发送失败则返回相应错误信息
     */
    @GetMapping("/ask-code")
    @Operation(summary = "请求邮件验证码")
    public RestBean<Void> askVerifyCode(@RequestParam @Email String email,
                                        @RequestParam @Pattern(regexp = "(register|reset|modify)")  String type,
                                        HttpServletRequest request){
        // 使用工具类处理消息响应，并调用账号服务发送验证码
        return utils.messageHandle(() ->
                accountService.registerEmailVerifyCode(type, String.valueOf(email), request.getRemoteAddr()));
    }

    /**
     * 用户注册接口
     * 通过邮箱注册新用户，需要先获取验证码
     * 
     * @param vo 注册信息对象，包含邮箱、验证码、用户名和密码
     * @return 操作结果，如果验证码错误、邮箱已注册或用户名已存在则返回相应错误信息
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册操作")
    public RestBean<Void> register(@RequestBody @Valid EmailRegisterVO vo){
        // 使用工具类处理消息响应，并调用账号服务注册新用户
        return utils.messageHandle(() ->
                accountService.registerEmailAccount(vo));
    }

    /**
     * 密码重置验证阶段
     * 在重置密码前，先验证邮箱和验证码是否正确
     * 
     * @param vo 验证信息对象，包含邮箱和验证码
     * @return 操作结果，如果验证码错误则返回相应错误信息
     */
    @PostMapping("/reset-confirm")
    @Operation(summary = "密码重置确认")
    public RestBean<Void> resetConfirm(@RequestBody @Valid ConfirmResetVO vo){
        // 使用工具类处理消息响应，并调用账号服务验证重置信息
        return utils.messageHandle(() -> accountService.resetConfirm(vo));
    }

    /**
     * 密码重置接口
     * 验证通过后，设置新密码
     * 
     * @param vo 重置信息对象，包含邮箱、验证码和新密码
     * @return 操作结果，如果验证码错误则返回相应错误信息
     */
    @PostMapping("/reset-password")
    @Operation(summary = "密码重置操作")
    public RestBean<Void> resetPassword(@RequestBody @Valid EmailResetVO vo){
        // 使用工具类处理消息响应，并调用账号服务重置密码
        return utils.messageHandle(() ->
                accountService.resetEmailAccountPassword(vo));
    }
}
