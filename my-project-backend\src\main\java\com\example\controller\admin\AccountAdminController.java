package com.example.controller.admin;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.entity.RestBean;
import com.example.entity.dto.Account;
import com.example.entity.dto.AccountDetails;
import com.example.entity.dto.AccountPrivacy;
import com.example.entity.vo.response.AccountVO;
import com.example.service.AccountDetailsService;
import com.example.service.AccountPrivacyService;
import com.example.service.AccountService;
import com.example.utils.Const;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 管理员用户管理控制器
 * 处理管理员对用户账号的各种管理操作，如查询用户列表、获取用户详情、保存用户信息等
 */
@RestController
@RequestMapping("/api/admin/user")
public class AccountAdminController {
    /**
     * 用户账号服务
     */
    @Resource
    AccountService service;
    
    /**
     * 用户详情服务
     */
    @Resource
    AccountDetailsService accountDetailService;
    
    /**
     * 用户隐私设置服务
     */
    @Resource
    AccountPrivacyService accountPrivacyService;
    
    /**
     * Redis模板，用于处理封禁用户信息
     */
    @Resource
    StringRedisTemplate template;
    
    /**
     * JWT令牌过期时间，单位：小时，从配置文件中注入
     */
    @Value("${spring.security.jwt.expire}")
    private int expire;

    /**
     * 分页获取用户列表
     * 
     * @param page 页码
     * @param size 每页显示数量
     * @return 用户列表及总数量的JSON对象
     */
    @GetMapping("/list")
    public RestBean<JSONObject> list(int page, int size) {
        JSONObject json = new JSONObject();
        // 获取分页用户列表并转换为VO对象
        List<AccountVO> list = service.page(Page.of(page, size))
                .getRecords()
                .stream()
                .map(account -> account.asViewObject(AccountVO.class))
                .toList();
        // 添加总用户数
        json.put("total", service.count());
        // 添加用户列表
        json.put("list", list);
        System.out.println(list);
        return RestBean.success(json);
    }

    /**
     * 获取指定用户的详细信息
     * 
     * @param id 用户ID
     * @return 包含用户详情和隐私设置的JSON对象
     */
    @GetMapping("/detail")
    public RestBean<JSONObject> accountDetail(int id) {
        JSONObject json = new JSONObject();
        // 获取用户详情信息
        json.put("detail", accountDetailService.findAccountDetailsById(id));
        // 获取用户隐私设置
        json.put("privacy", accountPrivacyService.accountPrivacy(id));
        return RestBean.success(json);
    }
    
    /**
     * 保存/更新用户信息
     * 包括基本信息、详细信息和隐私设置
     * 
     * @param json 包含用户信息的JSON对象
     * @return 操作结果
     */
    @PostMapping("/save")
    public RestBean<Void> save(@RequestBody JSONObject json) {
        // 获取用户ID
        int id=json.getInteger("id");
        // 获取当前数据库中的用户信息
        Account account=service.findAccountById(id);
        // 从JSON中获取新的用户信息
        Account save=json.toJavaObject(Account.class);
        // 处理用户封禁状态变更
        handleBanned(account, save);
        // 复制新的用户信息到当前用户对象，但排除密码和注册时间字段
        BeanUtils.copyProperties(save, account, "password", "registerTime");
        // 更新用户基本信息
        service.saveOrUpdate(account);
        
        // 更新用户详情信息
        AccountDetails details=accountDetailService.findAccountDetailsById(id);
        AccountDetails saveDetails=json.getJSONObject("detail").toJavaObject(AccountDetails.class);
        BeanUtils.copyProperties(saveDetails, details);
        accountDetailService.saveOrUpdate(details);
        
        // 更新用户隐私设置
        AccountPrivacy privacy=accountPrivacyService.accountPrivacy(id);
        AccountPrivacy savePrivacy=json.getJSONObject("privacy").toJavaObject(AccountPrivacy.class);
        BeanUtils.copyProperties(savePrivacy, privacy);
        accountPrivacyService.saveOrUpdate(privacy);
        
        return RestBean.success();
    }
    
    /**
     * 处理用户封禁状态变更
     * 如果用户从封禁变为未封禁，则从Redis中删除封禁标记
     * 如果用户从未封禁变为封禁，则在Redis中添加封禁标记
     * 
     * @param old 旧的用户信息
     * @param current 新的用户信息
     */
    private void handleBanned(Account old, Account current){
       String key = Const.BANNED_BLOCK + old.getId();
        if (old.isBanned() && !current.isBanned()){
            // 解除封禁，删除Redis中的封禁标记
            template.delete(key);
        } else if (!old.isBanned() && current.isBanned()){
            // 设置封禁，在Redis中添加封禁标记
            template.opsForValue().set(key, "true", expire, TimeUnit.HOURS);
        }
    }
}
