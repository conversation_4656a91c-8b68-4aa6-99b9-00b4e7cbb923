package com.example.service;

import com.example.entity.dto.Account;
import com.example.entity.dto.StoreImage;
import com.example.mapper.AccountMapper;
import com.example.mapper.ImageStoreMapper;
import com.example.service.impl.ImageServiceImpl;
import com.example.utils.FlowUtils;
import io.minio.GetObjectResponse;
import io.minio.MinioClient;
import io.minio.errors.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ImageServiceTest {

    @Mock
    private ImageStoreMapper imageStoreMapper;
    
    @Mock
    private AccountMapper accountMapper;
    
    @Mock
    private MinioClient minioClient;
    
    @Mock
    private FlowUtils flowUtils;
    
    @Mock
    private MultipartFile file;
    
    @Mock
    private GetObjectResponse getObjectResponse;
    
    @InjectMocks
    private ImageServiceImpl imageService;
    
    @BeforeEach
    void setUp() {
        // 不需要初始化 testImage，因为 StoreImage 没有无参构造函数
    }
    
    @Test
    void testUploadImage_Success() throws Exception {
        // 准备测试数据
        when(flowUtils.limitPeriodCounterCheck(anyString(), anyInt(), anyInt())).thenReturn(true);
        when(file.getInputStream()).thenReturn(new ByteArrayInputStream("test image content".getBytes()));
        when(file.getSize()).thenReturn(1024L);
        
        // 模拟 UUID 生成固定值
        try (MockedStatic<UUID> mockedUuid = Mockito.mockStatic(UUID.class)) {
            UUID mockUuid = mock(UUID.class);
            when(mockUuid.toString()).thenReturn("test-uuid");
            mockedUuid.when(UUID::randomUUID).thenReturn(mockUuid);
            
            // 模拟 save 方法返回 true
            doReturn(true).when(imageService).save(any(StoreImage.class));
            
            // 执行测试
            String result = imageService.uploadImage(file, 1);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result.contains("test-uuid"));
            verify(minioClient).putObject(any());
        }
    }
    
    @Test
    void testUploadImage_FlowLimited() throws IOException {
        // 准备测试数据
        when(flowUtils.limitPeriodCounterCheck(anyString(), anyInt(), anyInt())).thenReturn(false);
        
        // 执行测试
        String result = imageService.uploadImage(file, 1);
        
        // 验证结果
        assertNull(result);
        verify(minioClient, never()).putObject(any());
    }
    
    @Test
    void testFetchImageFromMinio() throws Exception {
        // 准备测试数据
        OutputStream outputStream = new ByteArrayOutputStream();
        when(minioClient.getObject(any())).thenReturn(getObjectResponse);
        
        // 执行测试
        imageService.fetchImageFromMinio(outputStream, "test-image.jpg");
        
        // 验证结果
        verify(minioClient).getObject(any());
    }
    
    @Test
    void testUploadAvatar_Success() throws IOException, ServerException, InsufficientDataException, 
            ErrorResponseException, NoSuchAlgorithmException, InvalidKeyException, 
            InvalidResponseException, XmlParserException, InternalException {
        // 准备测试数据
        Account account = new Account();
        account.setAvatar("old-avatar.jpg");
        when(accountMapper.selectById(anyInt())).thenReturn(account);
        when(accountMapper.update(any(), any())).thenReturn(1);
        
        when(file.getInputStream()).thenReturn(new ByteArrayInputStream("test avatar content".getBytes()));
        when(file.getSize()).thenReturn(1024L);
        
        // 模拟 UUID 生成固定值
        try (MockedStatic<UUID> mockedUuid = Mockito.mockStatic(UUID.class)) {
            UUID mockUuid = mock(UUID.class);
            when(mockUuid.toString()).thenReturn("test-uuid");
            mockedUuid.when(UUID::randomUUID).thenReturn(mockUuid);
            
            // 执行测试
            String result = imageService.uploadAvatar(file, 1);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result.contains("test-uuid"));
            verify(minioClient).putObject(any());
            verify(minioClient).removeObject(any());
        }
    }
} 