package com.example.controller;

import com.example.entity.RestBean;
import com.example.service.ImageService;
import io.minio.errors.ErrorResponseException;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 对象存储访问控制器
 * 处理从MinIO对象存储服务获取图片的请求
 * 主要用于为前端提供图片访问服务，支持图片缓存控制
 */
@Slf4j
@RestController
public class ObjectController {

    /**
     * 图片服务，用于从MinIO获取图片
     */
    @Resource
    ImageService service;

    /**
     * 处理图片获取请求
     * 匹配/images/**路径，从MinIO获取对应的图片
     * 
     * @param request HTTP请求对象，用于获取图片路径
     * @param response HTTP响应对象，用于输出图片数据
     * @throws Exception 如果获取图片过程中发生错误
     */
    @GetMapping("/images/**")
    public void imageFetch(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置响应内容类型为图片
        response.setHeader("Content-Type", "image/jpg");
        // 调用内部方法获取图片
        this.fetchImage(request, response);
    }

    /**
     * 内部方法，处理图片获取的具体逻辑
     * 从请求路径中提取图片路径，然后从MinIO获取图片数据
     * 
     * @param request HTTP请求对象，用于获取图片路径
     * @param response HTTP响应对象，用于输出图片数据
     * @throws Exception 如果获取图片过程中发生错误
     */
    private void fetchImage(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 从请求路径中提取图片路径（去掉前面的"/images/"）
        String imagePath = request.getServletPath().substring(7);
        ServletOutputStream stream = response.getOutputStream();
        
        // 检查图片路径是否有效（长度太短的路径视为无效）
        if(imagePath.length() <= 13) {
            // 路径无效，返回404错误
            response.setStatus(404);
            stream.println(RestBean.failure(404, "Not found").toString());
        } else {
            try {
                // 从MinIO获取图片并写入响应输出流
                service.fetchImageFromMinio(stream, imagePath);
                // 设置缓存控制头，缓存有效期为30天（2592000秒）
                response.setHeader("Cache-Control", "max-age=2592000");
            } catch (ErrorResponseException e) {
                if(e.response().code() == 404) {
                    // MinIO返回404错误，表示图片不存在
                    response.setStatus(404);
                    stream.println(RestBean.failure(404, "Not found").toString());
                } else {
                    // 其他错误，记录日志
                    log.error("从Minio获取图片出现异常: "+e.getMessage(), e);
                }
            }
        }
    }
}
