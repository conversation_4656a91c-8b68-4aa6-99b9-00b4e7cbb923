package com.example.controller;

import com.example.entity.RestBean;
import com.example.entity.vo.response.NotificationVO;
import com.example.service.NotificationService;
import com.example.utils.Const;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.Min;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通知消息控制器
 * 处理用户通知消息相关的所有操作，包括：
 * - 获取用户的通知列表
 * - 删除指定通知
 * - 删除所有通知
 */
@RestController
@RequestMapping("/api/notification")
public class NotificationController {
    /**
     * 通知服务
     */
    @Resource
    NotificationService service;

    /**
     * 获取当前用户的所有通知消息
     * 
     * @param id 用户ID（从请求属性中获取）
     * @return 包含通知列表的响应对象
     */
    @GetMapping("/list")
    public RestBean<List<NotificationVO>> listNotification(@RequestAttribute(Const.ATTR_USER_ID) int id) {
        // 查询用户的所有通知并返回
        return RestBean.success(service.findUserNotification(id));
    }

    /**
     * 删除指定的通知消息
     * 
     * @param id 通知ID
     * @param uid 用户ID（从请求属性中获取）
     * @return 操作结果
     */
    @GetMapping("/delete")
    public RestBean<List<NotificationVO>> deleteNotification(@RequestParam @Min(0) int id,
                                                             @RequestAttribute(Const.ATTR_USER_ID) int uid) {
        // 删除指定用户的指定通知
        service.deleteUserNotification(id, uid);
        return RestBean.success();
    }

    /**
     * 删除当前用户的所有通知消息
     * 
     * @param uid 用户ID（从请求属性中获取）
     * @return 操作结果
     */
    @GetMapping("/delete-all")
    public RestBean<List<NotificationVO>> deleteAllNotification(@RequestAttribute(Const.ATTR_USER_ID) int uid) {
        // 删除用户的所有通知
        service.deleteUserAllNotification(uid);
        return RestBean.success();
    }
}
