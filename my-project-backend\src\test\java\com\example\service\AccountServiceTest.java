package com.example.service;

import com.example.entity.dto.Account;
import com.example.entity.vo.request.EmailRegisterVO;
import com.example.entity.vo.request.ConfirmResetVO;
import com.example.entity.vo.request.EmailResetVO;
import com.example.entity.vo.request.ModifyEmailVO;
import com.example.entity.vo.request.ChangePasswordVO;
import com.example.mapper.AccountDetailsMapper;
import com.example.mapper.AccountMapper;
import com.example.mapper.AccountPrivacyMapper;
import com.example.service.impl.AccountServiceImpl;
import com.example.utils.FlowUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AccountServiceTest {

    @Mock
    private AccountMapper accountMapper;
    
    @Mock
    private AccountDetailsMapper detailsMapper;
    
    @Mock
    private AccountPrivacyMapper privacyMapper;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @Mock
    private StringRedisTemplate stringRedisTemplate;
    
    @Mock
    private ValueOperations<String, String> valueOperations;
    
    @Mock
    private AmqpTemplate rabbitTemplate;
    
    @Mock
    private FlowUtils flowUtils;
    
    @InjectMocks
    private AccountServiceImpl accountService;
    
    private Account testAccount;
    
    @BeforeEach
    void setUp() {
        testAccount = new Account(1, "test", "password", "<EMAIL>", "user", null, new Date(), false, false);
        
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
    }
    
    @Test
    void testLoadUserByUsername_Success() {
        // 准备测试数据
        when(accountMapper.selectOne(any())).thenReturn(testAccount);
        
        // 执行测试
        UserDetails userDetails = accountService.loadUserByUsername("test");
        
        // 验证结果
        assertNotNull(userDetails);
        assertEquals("test", userDetails.getUsername());
        assertEquals("password", userDetails.getPassword());
        assertTrue(userDetails.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_user")));
    }
    
    @Test
    void testLoadUserByUsername_UserNotFound() {
        // 准备测试数据
        when(accountMapper.selectOne(any())).thenReturn(null);
        
        // 执行测试并验证结果
        assertThrows(UsernameNotFoundException.class, () -> {
            accountService.loadUserByUsername("nonexistent");
        });
    }
    
    @Test
    void testRegisterEmailVerifyCode_Success() {
        // 准备测试数据
        when(flowUtils.limitOnceCheck(anyString(), anyInt())).thenReturn(true);
        
        // 执行测试
        String result = accountService.registerEmailVerifyCode("register", "<EMAIL>", "127.0.0.1");
        
        // 验证结果
        assertNull(result);
        verify(rabbitTemplate).convertAndSend(anyString(), anyMap());
        verify(valueOperations).set(anyString(), anyString(), anyLong(), any());
    }
    
    @Test
    void testRegisterEmailVerifyCode_LimitExceeded() {
        // 准备测试数据
        when(flowUtils.limitOnceCheck(anyString(), anyInt())).thenReturn(false);
        
        // 执行测试
        String result = accountService.registerEmailVerifyCode("register", "<EMAIL>", "127.0.0.1");
        
        // 验证结果
        assertEquals("请求频繁，请稍后再试", result);
        verify(rabbitTemplate, never()).convertAndSend(anyString(), anyMap());
        verify(valueOperations, never()).set(anyString(), anyString(), anyLong(), any());
    }
    
    @Test
    void testRegisterEmailAccount_Success() {
        // 准备测试数据
        EmailRegisterVO registerVO = new EmailRegisterVO();
        registerVO.setEmail("<EMAIL>");
        registerVO.setUsername("newuser");
        registerVO.setPassword("password");
        registerVO.setCode("123456");
        
        when(valueOperations.get(anyString())).thenReturn("123456");
        when(accountMapper.exists(any())).thenReturn(false);
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(accountMapper.insert(any())).thenReturn(1);
        
        // 执行测试
        String result = accountService.registerEmailAccount(registerVO);
        
        // 验证结果
        assertNull(result);
        verify(accountMapper).insert(any());
        verify(privacyMapper).insert(any());
        verify(detailsMapper).insert(any());
        verify(valueOperations).get(anyString());
        verify(stringRedisTemplate).delete(anyString());
    }
    
    @Test
    void testRegisterEmailAccount_InvalidCode() {
        // 准备测试数据
        EmailRegisterVO registerVO = new EmailRegisterVO();
        registerVO.setEmail("<EMAIL>");
        registerVO.setUsername("newuser");
        registerVO.setPassword("password");
        registerVO.setCode("123456");
        
        when(valueOperations.get(anyString())).thenReturn("654321"); // 不匹配的验证码
        
        // 执行测试
        String result = accountService.registerEmailAccount(registerVO);
        
        // 验证结果
        assertEquals("验证码错误，请重新输入", result);
        verify(accountMapper, never()).insert(any());
    }
    
    @Test
    void testRegisterEmailAccount_EmailExists() {
        // 准备测试数据
        EmailRegisterVO registerVO = new EmailRegisterVO();
        registerVO.setEmail("<EMAIL>");
        registerVO.setUsername("newuser");
        registerVO.setPassword("password");
        registerVO.setCode("123456");
        
        when(valueOperations.get(anyString())).thenReturn("123456");
        when(accountMapper.exists(any())).thenReturn(true); // 邮箱已存在
        
        // 执行测试
        String result = accountService.registerEmailAccount(registerVO);
        
        // 验证结果
        assertEquals("该邮件地址已被注册", result);
        verify(accountMapper, never()).insert(any());
    }
    
    @Test
    void testResetEmailAccountPassword_Success() {
        // 准备测试数据
        EmailResetVO resetVO = new EmailResetVO();
        resetVO.setEmail("<EMAIL>");
        resetVO.setPassword("newpassword");
        resetVO.setCode("123456");
        
        when(valueOperations.get(anyString())).thenReturn("123456");
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(accountMapper.update(any(), any())).thenReturn(1);
        
        // 执行测试
        String result = accountService.resetEmailAccountPassword(resetVO);
        
        // 验证结果
        assertNull(result);
        verify(accountMapper).update(any(), any());
        verify(stringRedisTemplate).delete(anyString());
    }
    
    @Test
    void testModifyEmail_Success() {
        // 准备测试数据
        ModifyEmailVO modifyEmailVO = new ModifyEmailVO();
        modifyEmailVO.setEmail("<EMAIL>");
        modifyEmailVO.setCode("123456");
        
        when(valueOperations.get(anyString())).thenReturn("123456");
        when(accountMapper.selectOne(any())).thenReturn(null); // 邮箱未被使用
        when(accountMapper.update(any(), any())).thenReturn(1);
        
        // 执行测试
        String result = accountService.modifyEmail(1, modifyEmailVO);
        
        // 验证结果
        assertNull(result);
        verify(accountMapper).update(any(), any());
        verify(stringRedisTemplate).delete(anyString());
    }
    
    @Test
    void testChangePassword_Success() {
        // 准备测试数据
        ChangePasswordVO changePasswordVO = new ChangePasswordVO();
        changePasswordVO.setPassword("oldpassword");
        changePasswordVO.setNew_password("newpassword");
        
        when(accountMapper.selectOne(any())).thenReturn(testAccount);
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(accountMapper.update(any(), any())).thenReturn(1);
        
        // 执行测试
        String result = accountService.changePassword(1, changePasswordVO);
        
        // 验证结果
        assertNull(result);
        verify(accountMapper).update(any(), any());
    }
    
    @Test
    void testChangePassword_WrongPassword() {
        // 准备测试数据
        ChangePasswordVO changePasswordVO = new ChangePasswordVO();
        changePasswordVO.setPassword("wrongpassword");
        changePasswordVO.setNew_password("newpassword");
        
        when(accountMapper.selectOne(any())).thenReturn(testAccount);
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(false);
        
        // 执行测试
        String result = accountService.changePassword(1, changePasswordVO);
        
        // 验证结果
        assertEquals("原密码错误，请重新输入！", result);
        verify(accountMapper, never()).update(any(), any());
    }
} 