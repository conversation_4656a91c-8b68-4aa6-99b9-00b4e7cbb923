package com.example.config;

import com.example.entity.RestBean;
import com.example.entity.dto.Account;
import com.example.entity.vo.response.AuthorizeVO;
import com.example.filter.JwtAuthenticationFilter;
import com.example.filter.RequestLogFilter;
import com.example.service.AccountService;
import com.example.utils.Const;
import com.example.utils.JwtUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.io.IOException;
import java.io.PrintWriter;

/**
 * SpringSecurity相关配置类
 * 负责整个应用的安全配置，包括：
 * - URL访问权限控制
 * - 登录/登出流程处理
 * - JWT令牌验证
 * - 过滤器链配置
 * - 会话管理
 */
@Configuration
public class SecurityConfiguration {

    /**
     * JWT认证过滤器，用于验证请求中的JWT令牌
     */
    @Resource
    JwtAuthenticationFilter jwtAuthenticationFilter;

    /**
     * 请求日志过滤器，用于记录请求日志
     */
    @Resource
    RequestLogFilter requestLogFilter;

    /**
     * JWT工具类，用于生成、验证JWT令牌
     */
    @Resource
    JwtUtils utils;

    /**
     * 账户服务，用于获取用户信息
     */
    @Resource
    AccountService service;

    /**
     * 配置Spring Security的安全过滤器链
     * 针对于SpringSecurity 6的新版配置方法，采用函数式编程风格
     * 
     * @param http 安全配置器
     * @return 配置完成的安全过滤器链
     * @throws Exception 可能抛出的异常
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
                // 配置请求授权规则
                .authorizeHttpRequests(conf -> conf
                        // 允许所有人访问登录、注册等认证相关接口和错误页面
                        .requestMatchers("/api/auth/**", "/error").permitAll()
                        // 允许所有人访问图片资源
                        .requestMatchers("/images/**").permitAll()
                        // 允许所有人访问Swagger API文档
                        .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                        // 管理员接口仅管理员可访问
                        .requestMatchers("/api/admin/**").hasRole(Const.ROLE_ADMIN)
                        // 其他请求需要普通用户或管理员角色
                        .anyRequest().hasAnyRole(Const.ROLE_DEFAULT, Const.ROLE_ADMIN)
                )
                // 配置表单登录
                .formLogin(conf -> conf
                        // 设置登录接口地址
                        .loginProcessingUrl("/api/auth/login")
                        // 登录失败处理
                        .failureHandler(this::handleProcess)
                        // 登录成功处理
                        .successHandler(this::handleProcess)
                        .permitAll()
                )
                // 配置登出
                .logout(conf -> conf
                        // 设置登出接口地址
                        .logoutUrl("/api/auth/logout")
                        // 登出成功处理
                        .logoutSuccessHandler(this::onLogoutSuccess)
                )
                // 配置异常处理
                .exceptionHandling(conf -> conf
                        // 访问被拒绝处理
                        .accessDeniedHandler(this::handleProcess)
                        // 未认证处理
                        .authenticationEntryPoint(this::handleProcess)
                )
                // 禁用CSRF保护，因为使用JWT进行认证
                .csrf(AbstractHttpConfigurer::disable)
                // 配置会话管理，使用无状态会话（不使用Session）
                .sessionManagement(conf -> conf
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                // 添加请求日志过滤器，在用户名密码认证过滤器之前
                .addFilterBefore(requestLogFilter, UsernamePasswordAuthenticationFilter.class)
                // 添加JWT认证过滤器，在请求日志过滤器之前
                .addFilterBefore(jwtAuthenticationFilter, RequestLogFilter.class)
                .build();
    }

    /**
     * 统一处理多种认证场景的处理方法
     * 将多种类型的Handler整合到同一个方法中，包含：
     * - 登录成功：生成JWT令牌并返回
     * - 登录失败：返回错误信息
     * - 访问被拒绝：返回403错误
     * - 未认证：返回401错误
     * 
     * @param request 请求对象
     * @param response 响应对象
     * @param exceptionOrAuthentication 异常或认证对象
     * @throws IOException IO异常
     */
    private void handleProcess(HttpServletRequest request,
                               HttpServletResponse response,
                               Object exceptionOrAuthentication) throws IOException {
        // 设置响应内容类型为JSON
        response.setContentType("application/json;charset=utf-8");
        PrintWriter writer = response.getWriter();
        
        // 处理访问被拒绝异常
        if(exceptionOrAuthentication instanceof AccessDeniedException exception) {
            writer.write(RestBean
                    .forbidden(exception.getMessage()).asJsonString());
        } 
        // 处理其他异常（通常是未认证）
        else if(exceptionOrAuthentication instanceof Exception exception) {
            writer.write(RestBean
                    .unauthorized(exception.getMessage()).asJsonString());
        } 
        // 处理认证成功
        else if(exceptionOrAuthentication instanceof Authentication authentication){
            // 获取认证用户信息
            User user = (User) authentication.getPrincipal();
            Account account = service.findAccountByNameOrEmail(user.getUsername());
            
            // 检查用户是否被封禁
            if(account.isBanned()){
                writer.write(RestBean.forbidden("该账号已被封禁").asJsonString());
                return;
            }
            
            // 创建JWT令牌
            String jwt = utils.createJwt(user, account.getUsername(), account.getId());
            if(jwt == null) {
                // 令牌创建失败（可能是因为请求过于频繁）
                writer.write(RestBean.forbidden("登录验证频繁，请稍后再试").asJsonString());
            } else {
                // 创建认证响应对象并设置令牌
                AuthorizeVO vo = account.asViewObject(AuthorizeVO.class, o -> o.setToken(jwt));
                // 设置令牌过期时间
                vo.setExpire(utils.expireTime());
                // 返回成功响应
                writer.write(RestBean.success(vo).asJsonString());
            }
        }
    }

    /**
     * 退出登录成功处理方法
     * 将当前JWT令牌加入黑名单，使其失效
     * 
     * @param request 请求对象
     * @param response 响应对象
     * @param authentication 认证对象
     * @throws IOException IO异常
     */
    private void onLogoutSuccess(HttpServletRequest request,
                                 HttpServletResponse response,
                                 Authentication authentication) throws IOException {
        // 设置响应内容类型为JSON
        response.setContentType("application/json;charset=utf-8");
        PrintWriter writer = response.getWriter();
        
        // 从请求头中获取JWT令牌
        String authorization = request.getHeader("Authorization");
        // 使JWT令牌失效（加入黑名单）
        if(utils.invalidateJwt(authorization)) {
            writer.write(RestBean.success("退出登录成功").asJsonString());
            return;
        }
    }
}
