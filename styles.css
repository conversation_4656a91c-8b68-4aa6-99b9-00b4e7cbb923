/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: <PERSON><PERSON>, "PingFang SC", "Microsoft YaHei", sans-serif;
}

body {
    background-color: #fff;
    color: #333;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

a {
    text-decoration: none;
    color: #333;
}

a:hover {
    color: #4e6ef2;
}

.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏样式 */
.header {
    padding: 20px 24px 0;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.nav-left {
    display: flex;
    align-items: center;
}

.weather {
    color: #9195a3;
}

.nav-right {
    display: flex;
    align-items: center;
}

.nav-right a {
    margin-left: 20px;
    color: #222;
}

.nav-right a:hover {
    color: #4e6ef2;
}

.login {
    background-color: #4e6ef2;
    color: #fff !important;
    padding: 6px 14px;
    border-radius: 6px;
}

.login:hover {
    background-color: #4662d9;
}

/* 主要内容区样式 */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    margin-top: -80px;
}

/* Logo样式 */
.logo {
    margin-bottom: 30px;
    text-align: center;
}

.logo img {
    width: 270px;
    height: auto;
}

/* 搜索区域样式 */
.search-area {
    width: 100%;
    max-width: 654px;
    margin: 0 auto;
}

.search-container {
    width: 100%;
}

.search-box {
    display: flex;
    align-items: center;
    border: 2px solid #4e6ef2;
    border-radius: 10px;
    padding: 0 16px;
    height: 44px;
    position: relative;
    background-color: #fff;
}

#search-input {
    flex: 1;
    height: 100%;
    border: none;
    outline: none;
    font-size: 16px;
    color: #222;
}

.voice-icon, .camera-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-left: 10px;
    cursor: pointer;
}

.search-buttons {
    display: flex;
    justify-content: center;
    margin-top: 12px;
    gap: 12px;
}

.search-btn, .lucky-btn {
    padding: 9px 15px;
    font-size: 15px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-btn {
    background-color: #4e6ef2;
    color: #fff;
    min-width: 108px;
}

.search-btn:hover {
    background-color: #4662d9;
}

.lucky-btn {
    background-color: #f5f5f6;
    color: #333;
    min-width: 108px;
}

.lucky-btn:hover {
    background-color: #e8e8e8;
}

/* 快捷方式区域样式 */
.shortcuts {
    margin-top: 40px;
    width: 100%;
    max-width: 654px;
}

.shortcut-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 10px;
}

.shortcut-title span {
    font-size: 14px;
    color: #9195a3;
}

.edit {
    font-size: 13px;
    color: #9195a3;
}

.shortcut-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.shortcut-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 84px;
    margin-bottom: 16px;
}

.shortcut-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    margin-bottom: 8px;
    background-color: #f5f5f6;
    display: flex;
    align-items: center;
    justify-content: center;
    background-size: 24px;
    background-repeat: no-repeat;
    background-position: center;
}

.shortcut-item span {
    font-size: 13px;
    color: #222;
}

.news {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234e6ef2"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>');
}

.map {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234e6ef2"><path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"/></svg>');
}

.tieba {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234e6ef2"><path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/></svg>');
}

.video {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234e6ef2"><path d="M18 4v1h-2V4c0-.55-.45-1-1-1H9c-.55 0-1 .45-1 1v1H6V4c0-.55-.45-1-1-1s-1 .45-1 1v16c0 .55.45 1 1 1s1-.45 1-1v-1h2v1c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-1h2v1c0 .55.45 1 1 1s1-.45 1-1V4c0-.55-.45-1-1-1s-1 .45-1 1zM8 17H6v-2h2v2zm0-4H6v-2h2v2zm0-4H6V7h2v2zm10 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V7h2v2z"/></svg>');
}

.image {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234e6ef2"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>');
}

.translate {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234e6ef2"><path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/></svg>');
}

/* 底部区域样式 */
.footer {
    margin-top: auto;
    padding: 40px 0 20px;
    background-color: #f5f5f6;
    text-align: center;
}

.footer-content {
    max-width: 660px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 10px;
}

.footer-links a {
    margin: 0 10px 10px;
    font-size: 12px;
    color: #9195a3;
}

.copyright {
    font-size: 12px;
    color: #9195a3;
}

.copyright a {
    margin: 0 5px;
    color: #9195a3;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main {
        margin-top: 0;
        padding-top: 60px;
    }
    
    .logo img {
        width: 200px;
    }
    
    .nav-right a {
        margin-left: 12px;
    }
    
    .nav-right a:nth-child(n+4):nth-child(-n+7) {
        display: none;
    }
    
    .search-box {
        height: 40px;
    }
    
    .shortcut-items {
        justify-content: space-around;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 10px 16px 0;
    }
    
    .nav-right a {
        margin-left: 8px;
    }
    
    .nav-right a:nth-child(n+3):nth-child(-n+7) {
        display: none;
    }
    
    .logo img {
        width: 160px;
    }
    
    .search-buttons {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }
    
    .search-btn, .lucky-btn {
        width: 100%;
        max-width: 200px;
    }
    
    .footer-links a {
        margin: 0 5px 8px;
    }
}