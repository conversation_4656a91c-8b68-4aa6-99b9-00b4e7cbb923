package com.example.integration;

import com.alibaba.fastjson2.JSONObject;
import com.example.entity.RestBean;
import com.example.entity.vo.request.AddCommentVO;
import com.example.entity.vo.request.TopicCreateVO;
import com.example.entity.vo.response.TopicDetailVO;
import com.example.entity.vo.response.TopicPreviewVO;
import com.example.entity.vo.response.TopicTypeVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Topic 相关功能的集成测试类
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
public class TopicIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试获取话题类型列表
     */
    @Test
    @WithMockUser(username = "test", roles = "user")
    public void testListTopicTypes() throws Exception {
        // 执行请求
        MvcResult result = mockMvc.perform(get("/api/forum/types"))
                .andExpect(status().isOk())
                .andReturn();

        // 解析响应结果
        String content = result.getResponse().getContentAsString();
        RestBean<List<TopicTypeVO>> response = objectMapper.readValue(content, 
                objectMapper.getTypeFactory().constructParametricType(RestBean.class, 
                objectMapper.getTypeFactory().constructCollectionType(List.class, TopicTypeVO.class)));

        // 验证结果
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertTrue(response.getData().size() > 0);
    }

    /**
     * 测试创建话题
     */
    @Test
    @WithMockUser(username = "test", roles = "user")
    public void testCreateTopic() throws Exception {
        // 准备测试数据
        TopicCreateVO createVO = new TopicCreateVO();
        createVO.setTitle("测试话题标题");
        JSONObject content = new JSONObject();
        content.put("ops", new JSONObject[]{new JSONObject().fluentPut("insert", "测试话题内容")});
        createVO.setContent(content);
        createVO.setType(1); // 假设类型 ID 为 1

        // 执行请求
        MvcResult result = mockMvc.perform(post("/api/forum/create-topic")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createVO)))
                .andExpect(status().isOk())
                .andReturn();

        // 解析响应结果
        String responseContent = result.getResponse().getContentAsString();
        RestBean<?> response = objectMapper.readValue(responseContent, RestBean.class);

        // 验证结果
        assertEquals(200, response.getCode());
    }

    /**
     * 测试获取话题列表
     */
    @Test
    @WithMockUser(username = "test", roles = "user")
    public void testListTopics() throws Exception {
        // 执行请求
        MvcResult result = mockMvc.perform(get("/api/forum/list-topic")
                .param("page", "1")
                .param("type", "0")) // 0 表示所有类型
                .andExpect(status().isOk())
                .andReturn();

        // 解析响应结果
        String content = result.getResponse().getContentAsString();
        RestBean<?> response = objectMapper.readValue(content, RestBean.class);

        // 验证结果
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
    }

    /**
     * 测试获取话题详情
     */
    @Test
    @WithMockUser(username = "test", roles = "user")
    public void testGetTopicDetail() throws Exception {
        // 执行请求 - 假设话题 ID 为 1
        MvcResult result = mockMvc.perform(get("/api/forum/topic-detail")
                .param("tid", "1"))
                .andExpect(status().isOk())
                .andReturn();

        // 解析响应结果
        String content = result.getResponse().getContentAsString();
        RestBean<TopicDetailVO> response = objectMapper.readValue(content, 
                objectMapper.getTypeFactory().constructParametricType(RestBean.class, TopicDetailVO.class));

        // 验证结果
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().getTid());
    }

    /**
     * 测试添加评论
     */
    @Test
    @WithMockUser(username = "test", roles = "user")
    public void testAddComment() throws Exception {
        // 准备测试数据
        AddCommentVO commentVO = new AddCommentVO();
        commentVO.setTid(1); // 假设话题 ID 为 1
        commentVO.setContent("这是一条测试评论");

        // 执行请求
        MvcResult result = mockMvc.perform(post("/api/forum/add-comment")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(commentVO)))
                .andExpect(status().isOk())
                .andReturn();

        // 解析响应结果
        String responseContent = result.getResponse().getContentAsString();
        RestBean<?> response = objectMapper.readValue(responseContent, RestBean.class);

        // 验证结果
        assertEquals(200, response.getCode());
    }

    /**
     * 测试获取话题评论
     */
    @Test
    @WithMockUser(username = "test", roles = "user")
    public void testListComments() throws Exception {
        // 执行请求 - 假设话题 ID 为 1
        MvcResult result = mockMvc.perform(get("/api/forum/comments")
                .param("tid", "1")
                .param("page", "1"))
                .andExpect(status().isOk())
                .andReturn();

        // 解析响应结果
        String content = result.getResponse().getContentAsString();
        RestBean<?> response = objectMapper.readValue(content, RestBean.class);

        // 验证结果
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
    }

    /**
     * 测试点赞话题
     */
    @Test
    @WithMockUser(username = "test", roles = "user")
    public void testInteractWithTopic() throws Exception {
        // 执行请求 - 假设话题 ID 为 1，交互类型为点赞
        MvcResult result = mockMvc.perform(get("/api/forum/interact")
                .param("tid", "1")
                .param("type", "like"))
                .andExpect(status().isOk())
                .andReturn();

        // 解析响应结果
        String content = result.getResponse().getContentAsString();
        RestBean<?> response = objectMapper.readValue(content, RestBean.class);

        // 验证结果
        assertEquals(200, response.getCode());
    }
} 