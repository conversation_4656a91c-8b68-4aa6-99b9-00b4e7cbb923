package com.example.controller;

import com.example.entity.RestBean;
import com.example.entity.vo.request.ConfirmResetVO;
import com.example.entity.vo.request.EmailRegisterVO;
import com.example.entity.vo.request.EmailResetVO;
import com.example.service.AccountService;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AuthorizeControllerTest {

    @Mock
    private AccountService accountService;
    
    @Mock
    private HttpServletRequest request;
    
    @InjectMocks
    private AuthorizeController authorizeController;
    
    @Test
    void testRegisterEmailVerifyCode_Success() {
        // 准备测试数据
        when(accountService.registerEmailVerifyCode(anyString(), anyString(), anyString())).thenReturn(null);
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        
        // 执行测试
        RestBean<Void> result = authorizeController.registerEmailVerifyCode("<EMAIL>");
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(accountService).registerEmailVerifyCode(eq("register"), eq("<EMAIL>"), eq("127.0.0.1"));
    }
    
    @Test
    void testRegisterEmailVerifyCode_Failure() {
        // 准备测试数据
        when(accountService.registerEmailVerifyCode(anyString(), anyString(), anyString())).thenReturn("发送验证码失败");
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        
        // 执行测试
        RestBean<Void> result = authorizeController.registerEmailVerifyCode("<EMAIL>");
        
        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("发送验证码失败", result.getMessage());
    }
    
    @Test
    void testEmailRegister_Success() {
        // 准备测试数据
        EmailRegisterVO vo = new EmailRegisterVO();
        vo.setEmail("<EMAIL>");
        vo.setUsername("testuser");
        vo.setPassword("password");
        vo.setCode("123456");
        
        when(accountService.registerEmailAccount(any(EmailRegisterVO.class))).thenReturn(null);
        
        // 执行测试
        RestBean<Void> result = authorizeController.emailRegister(vo);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
    }
    
    @Test
    void testEmailRegister_Failure() {
        // 准备测试数据
        EmailRegisterVO vo = new EmailRegisterVO();
        vo.setEmail("<EMAIL>");
        vo.setUsername("testuser");
        vo.setPassword("password");
        vo.setCode("123456");
        
        when(accountService.registerEmailAccount(any(EmailRegisterVO.class))).thenReturn("注册失败");
        
        // 执行测试
        RestBean<Void> result = authorizeController.emailRegister(vo);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("注册失败", result.getMessage());
    }
    
    @Test
    void testResetConfirm_Success() {
        // 准备测试数据
        ConfirmResetVO vo = new ConfirmResetVO();
        vo.setEmail("<EMAIL>");
        vo.setCode("123456");
        
        when(accountService.resetConfirm(anyString(), anyString())).thenReturn(true);
        
        // 执行测试
        RestBean<Void> result = authorizeController.resetConfirm(vo);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
    }
    
    @Test
    void testResetConfirm_Failure() {
        // 准备测试数据
        ConfirmResetVO vo = new ConfirmResetVO();
        vo.setEmail("<EMAIL>");
        vo.setCode("123456");
        
        when(accountService.resetConfirm(anyString(), anyString())).thenReturn(false);
        
        // 执行测试
        RestBean<Void> result = authorizeController.resetConfirm(vo);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("验证码错误", result.getMessage());
    }
    
    @Test
    void testResetEmailVerifyCode_Success() {
        // 准备测试数据
        when(accountService.registerEmailVerifyCode(anyString(), anyString(), anyString())).thenReturn(null);
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        
        // 执行测试
        RestBean<Void> result = authorizeController.resetEmailVerifyCode("<EMAIL>");
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(accountService).registerEmailVerifyCode(eq("reset"), eq("<EMAIL>"), eq("127.0.0.1"));
    }
    
    @Test
    void testResetEmailPassword_Success() {
        // 准备测试数据
        EmailResetVO vo = new EmailResetVO();
        vo.setEmail("<EMAIL>");
        vo.setPassword("newpassword");
        vo.setCode("123456");
        
        when(accountService.resetEmailAccountPassword(any(EmailResetVO.class))).thenReturn(null);
        
        // 执行测试
        RestBean<Void> result = authorizeController.resetEmailPassword(vo);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
    }
    
    @Test
    void testResetEmailPassword_Failure() {
        // 准备测试数据
        EmailResetVO vo = new EmailResetVO();
        vo.setEmail("<EMAIL>");
        vo.setPassword("newpassword");
        vo.setCode("123456");
        
        when(accountService.resetEmailAccountPassword(any(EmailResetVO.class))).thenReturn("重置密码失败");
        
        // 执行测试
        RestBean<Void> result = authorizeController.resetEmailPassword(vo);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("重置密码失败", result.getMessage());
    }
} 