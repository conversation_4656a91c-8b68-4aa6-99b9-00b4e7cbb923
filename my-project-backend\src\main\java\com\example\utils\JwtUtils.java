package com.example.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * JWT令牌工具类
 * 用于处理JWT令牌的生成、解析、验证和失效处理
 * 包含令牌频率限制和黑名单机制，防止令牌滥用
 */
@Component
public class JwtUtils {

    /**
     * 用于给JWT令牌签名校验的密钥
     * 从配置文件中注入
     */
    @Value("${spring.security.jwt.key}")
    private String key;
    
    /**
     * 令牌的过期时间，以小时为单位
     * 从配置文件中注入
     */
    @Value("${spring.security.jwt.expire}")
    private int expire;
    
    /**
     * 为用户生成JWT令牌的冷却时间，防止刷接口频繁登录生成令牌
     * 以秒为单位，从配置文件中注入
     */
    @Value("${spring.security.jwt.limit.base}")
    private int limit_base;
    
    /**
     * 用户如果继续恶意刷令牌，更严厉的封禁时间
     * 从配置文件中注入
     */
    @Value("${spring.security.jwt.limit.upgrade}")
    private int limit_upgrade;
    
    /**
     * 判定用户在冷却时间内，继续恶意刷令牌的次数
     * 从配置文件中注入
     */
    @Value("${spring.security.jwt.limit.frequency}")
    private int limit_frequency;

    /**
     * Redis模板，用于存储令牌黑名单
     */
    @Resource
    StringRedisTemplate template;

    /**
     * 流量控制工具类，用于限制令牌生成频率
     */
    @Resource
    FlowUtils utils;

    /**
     * 使指定JWT令牌失效
     * 将令牌加入黑名单，使其无法再次使用
     * 
     * @param headerToken 请求头中携带的令牌（格式为"Bearer xxx"）
     * @return 是否成功使令牌失效
     */
    public boolean invalidateJwt(String headerToken){
        // 转换并提取实际的令牌内容
        String token = this.convertToken(headerToken);
        Algorithm algorithm = Algorithm.HMAC256(key);
        JWTVerifier jwtVerifier = JWT.require(algorithm).build();
        try {
            // 验证令牌有效性
            DecodedJWT verify = jwtVerifier.verify(token);
            // 将令牌ID加入黑名单
            return deleteToken(verify.getId(), verify.getExpiresAt());
        } catch (JWTVerificationException e) {
            // 令牌验证失败
            return false;
        }
    }

    /**
     * 根据配置快速计算令牌过期时间
     * 
     * @return 过期时间Date对象
     */
    public Date expireTime() {
        Calendar calendar = Calendar.getInstance();
        // 在当前时间基础上增加配置的过期小时数
        calendar.add(Calendar.HOUR, expire);
        return calendar.getTime();
    }

    /**
     * 根据用户信息生成对应的JWT令牌
     * 
     * @param user 用户详情对象（Spring Security的UserDetails）
     * @param username 用户名
     * @param userId 用户ID
     * @return 生成的JWT令牌字符串，如果频率检查失败则返回null
     */
    public String createJwt(UserDetails user, String username, int userId) {
        // 检查用户生成令牌的频率
        if(this.frequencyCheck(userId)) {
            Algorithm algorithm = Algorithm.HMAC256(key);
            Date expire = this.expireTime();
            // 创建JWT令牌
            return JWT.create()
                    // 设置JWT ID，用于标识令牌
                    .withJWTId(UUID.randomUUID().toString())
                    // 添加用户ID声明
                    .withClaim("id", userId)
                    // 添加用户名声明
                    .withClaim("name", username)
                    // 添加用户权限声明
                    .withClaim("authorities", user.getAuthorities()
                            .stream()
                            .map(GrantedAuthority::getAuthority).toList())
                    // 设置过期时间
                    .withExpiresAt(expire)
                    // 设置签发时间
                    .withIssuedAt(new Date())
                    // 使用算法签名
                    .sign(algorithm);
        } else {
            // 频率检查失败，不生成令牌
            return null;
        }
    }

    /**
     * 解析JWT令牌
     * 验证令牌的有效性并返回解码后的JWT对象
     * 
     * @param headerToken 请求头中携带的令牌（格式为"Bearer xxx"）
     * @return 解码后的JWT对象，如果令牌无效则返回null
     */
    public DecodedJWT resolveJwt(String headerToken){
        // 转换并提取实际的令牌内容
        String token = this.convertToken(headerToken);
        if(token == null) return null;
        
        Algorithm algorithm = Algorithm.HMAC256(key);
        JWTVerifier jwtVerifier = JWT.require(algorithm).build();
        try {
            // 验证令牌
            DecodedJWT verify = jwtVerifier.verify(token);
            // 检查令牌是否在黑名单中
            if(this.isInvalidToken(verify.getId())) return null;
            
            Map<String, Claim> claims = verify.getClaims();
            // 检查令牌是否已过期
            return new Date().after(claims.get("exp").asDate()) ? null : verify;
        } catch (JWTVerificationException e) {
            // 令牌验证失败
            return null;
        }
    }

    /**
     * 将JWT对象中的内容封装为Spring Security的UserDetails对象
     * 
     * @param jwt 已解析的JWT对象
     * @return 封装后的UserDetails对象
     */
    public UserDetails toUser(DecodedJWT jwt) {
        Map<String, Claim> claims = jwt.getClaims();
        // 构建UserDetails对象
        return User
                .withUsername(claims.get("name").asString())
                .password("******") // 密码不重要，因为已经通过JWT验证
                .authorities(claims.get("authorities").asArray(String.class))
                .build();
    }

    /**
     * 从JWT对象中提取用户ID
     * 
     * @param jwt 已解析的JWT对象
     * @return 用户ID
     */
    public Integer toId(DecodedJWT jwt) {
        Map<String, Claim> claims = jwt.getClaims();
        return claims.get("id").asInt();
    }

    /**
     * 频率检测，防止用户高频申请JWT令牌
     * 采用阶段封禁机制，如果用户在被提示无法登录后继续尝试，将增加封禁时间
     * 
     * @param userId 用户ID
     * @return 是否通过频率检测
     */
    private boolean frequencyCheck(int userId){
        String key = Const.JWT_FREQUENCY + userId;
        return utils.limitOnceUpgradeCheck(key, limit_frequency, limit_base, limit_upgrade);
    }

    /**
     * 校验并转换请求头中的Token令牌
     * 从"Bearer xxx"格式中提取实际的令牌内容
     * 
     * @param headerToken 请求头中的Token
     * @return 转换后的令牌，如果格式不正确则返回null
     */
    private String convertToken(String headerToken){
        if(headerToken == null || !headerToken.startsWith("Bearer "))
            return null;
        return headerToken.substring(7);
    }

    /**
     * 将Token列入Redis黑名单中
     * 使令牌在剩余有效期内无法使用
     * 
     * @param uuid 令牌ID
     * @param time 令牌过期时间
     * @return 是否操作成功
     */
    private boolean deleteToken(String uuid, Date time){
        // 检查令牌是否已在黑名单中
        if(this.isInvalidToken(uuid))
            return false;
            
        Date now = new Date();
        // 计算令牌剩余有效期（毫秒）
        long expire = Math.max(time.getTime() - now.getTime(), 0);
        // 将令牌ID添加到Redis黑名单，过期时间设为令牌剩余有效期
        template.opsForValue().set(Const.JWT_BLACK_LIST + uuid, "", expire, TimeUnit.MILLISECONDS);
        return true;
    }

    /**
     * 验证Token是否已被列入Redis黑名单
     * 
     * @param uuid 令牌ID
     * @return 是否在黑名单中
     */
    private boolean isInvalidToken(String uuid){
        return Boolean.TRUE.equals(template.hasKey(Const.JWT_BLACK_LIST + uuid));
    }
}
