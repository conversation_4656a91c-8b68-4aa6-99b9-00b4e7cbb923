package com.example.integration;

import com.example.entity.RestBean;
import com.example.entity.dto.Account;
import com.example.entity.vo.request.EmailRegisterVO;
import com.example.entity.vo.request.ConfirmResetVO;
import com.example.entity.vo.response.AccountVO;
import com.example.service.AccountService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Account 相关功能的集成测试类
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
public class AccountIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AccountService accountService;

    /**
     * 测试用户登录成功
     */
    @Test
    public void testLoginSuccess() throws Exception {
        // 执行登录请求
        MvcResult result = mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("username", "test")
                .param("password", "123456"))
                .andExpect(status().isOk())
                .andReturn();

        // 解析响应结果
        String content = result.getResponse().getContentAsString();
        RestBean<AccountVO> response = objectMapper.readValue(content, 
                objectMapper.getTypeFactory().constructParametricType(RestBean.class, AccountVO.class));

        // 验证结果
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals("test", response.getData().getUsername());
    }

    /**
     * 测试用户登录失败（密码错误）
     */
    @Test
    public void testLoginFailure_WrongPassword() throws Exception {
        // 执行登录请求
        MvcResult result = mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("username", "test")
                .param("password", "wrong_password"))
                .andExpect(status().isUnauthorized())
                .andReturn();

        // 解析响应结果
        String content = result.getResponse().getContentAsString();
        RestBean<?> response = objectMapper.readValue(content, RestBean.class);

        // 验证结果
        assertEquals(401, response.getCode());
        assertNotNull(response.getMessage());
    }

    /**
     * 测试用户注册流程
     */
    @Test
    public void testRegisterProcess() throws Exception {
        // 1. 发送验证码
        mockMvc.perform(get("/api/auth/register-email-verify-code")
                .param("email", "<EMAIL>"))
                .andExpect(status().isOk());

        // 2. 注册用户
        EmailRegisterVO registerVO = new EmailRegisterVO();
        registerVO.setEmail("<EMAIL>");
        registerVO.setUsername("new_user");
        registerVO.setPassword("password123");
        registerVO.setCode("123456"); // 假设验证码是 123456

        // 注意：由于验证码是通过 Redis 存储的，在集成测试中无法真正验证
        // 这里我们只测试接口调用是否正常，不验证实际注册结果

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerVO)))
                .andExpect(status().is4xxClientError()); // 由于验证码不正确，预期返回 4xx
    }

    /**
     * 测试获取当前登录用户信息
     */
    @Test
    @WithMockUser(username = "test", roles = "user")
    public void testGetCurrentUserInfo() throws Exception {
        // 执行请求
        MvcResult result = mockMvc.perform(get("/api/user/info"))
                .andExpect(status().isOk())
                .andReturn();

        // 解析响应结果
        String content = result.getResponse().getContentAsString();
        RestBean<AccountVO> response = objectMapper.readValue(content, 
                objectMapper.getTypeFactory().constructParametricType(RestBean.class, AccountVO.class));

        // 验证结果
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals("test", response.getData().getUsername());
    }

    /**
     * 测试重置密码流程
     */
    @Test
    public void testResetPasswordProcess() throws Exception {
        // 1. 发送重置密码验证码
        mockMvc.perform(get("/api/auth/reset-email-verify-code")
                .param("email", "<EMAIL>"))
                .andExpect(status().isOk());

        // 2. 确认验证码
        ConfirmResetVO confirmVO = new ConfirmResetVO();
        confirmVO.setEmail("<EMAIL>");
        confirmVO.setCode("123456"); // 假设验证码是 123456

        // 注意：由于验证码是通过 Redis 存储的，在集成测试中无法真正验证
        mockMvc.perform(post("/api/auth/reset-confirm")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(confirmVO)))
                .andExpect(status().is4xxClientError()); // 由于验证码不正确，预期返回 4xx
    }
} 