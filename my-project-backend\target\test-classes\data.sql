-- 插入测试用户
INSERT INTO `account` (`id`, `username`, `password`, `email`, `role`, `avatar`, `register_time`, `banned`, `top`) VALUES
(1, 'admin', '$2a$10$RpFJjxYiCdVKku2BwoRP9.ZT8WQR8a7rKfYZF6jlAr9j9maJava36', '<EMAIL>', 'admin', NULL, '2023-01-01 00:00:00', 0, 0),
(2, 'test', '$2a$10$RpFJjxYiCdVKku2BwoRP9.ZT8WQR8a7rKfYZF6jlAr9j9maJava36', '<EMAIL>', 'user', NULL, '2023-01-02 00:00:00', 0, 0),
(3, 'user1', '$2a$10$RpFJjxYiCdVKku2BwoRP9.ZT8WQR8a7rKfYZF6jlAr9j9maJava36', '<EMAIL>', 'user', NULL, '2023-01-03 00:00:00', 0, 0);

-- 插入用户详情
INSERT INTO `account_details` (`id`, `gender`, `phone`, `qq`, `wx`, `desc`, `birthday`, `location`) VALUES
(1, '男', '***********', '123456', 'admin-wx', '管理员账号', '1990-01-01', '北京'),
(2, '女', '***********', '234567', 'test-wx', '测试账号', '1995-05-05', '上海'),
(3, '男', '***********', '345678', 'user1-wx', '普通用户账号', '2000-10-10', '广州');

-- 插入用户隐私设置
INSERT INTO `account_privacy` (`id`, `phone`, `email`, `qq`, `wx`, `gender`, `birthday`, `location`) VALUES
(1, 0, 0, 0, 0, 0, 0, 0),
(2, 1, 0, 1, 1, 0, 0, 0),
(3, 1, 1, 1, 1, 1, 1, 1);

-- 插入话题类型
INSERT INTO `topic_type` (`id`, `name`, `desc`, `color`) VALUES
(1, '公告', '网站公告', '#FF5722'),
(2, '分享', '分享经验', '#2196F3'),
(3, '讨论', '技术讨论', '#4CAF50'),
(4, '求助', '技术求助', '#FFC107');

-- 插入话题
INSERT INTO `topic` (`id`, `title`, `content`, `uid`, `type`, `time`, `top`) VALUES
(1, '网站公告：测试版本上线', '{"ops":[{"insert":"网站测试版本现已上线，欢迎大家体验！\\n"}]}', 1, 1, '2023-01-10 10:00:00', 1),
(2, '分享一些学习经验', '{"ops":[{"insert":"这里是我的一些学习经验分享，希望对大家有帮助。\\n"}]}', 2, 2, '2023-01-15 14:30:00', 0),
(3, '关于Java多线程的讨论', '{"ops":[{"insert":"大家对Java多线程有什么看法？\\n"}]}', 3, 3, '2023-01-20 16:45:00', 0);

-- 插入话题评论
INSERT INTO `topic_comment` (`id`, `uid`, `tid`, `content`, `time`, `quote`) VALUES
(1, 2, 1, '{"ops":[{"insert":"感谢网站上线，期待更多功能！"}]}', '2023-01-10 11:30:00', 0),
(2, 3, 1, '{"ops":[{"insert":"网站很好用，界面很友好。"}]}', '2023-01-10 13:45:00', 0),
(3, 1, 2, '{"ops":[{"insert":"分享得很好，谢谢！"}]}', '2023-01-15 15:20:00', 0),
(4, 3, 2, '{"ops":[{"insert":"学到了很多，感谢分享。"}]}', '2023-01-15 16:10:00', 0),
(5, 1, 3, '{"ops":[{"insert":"Java多线程是一个很重要的话题。"}]}', '2023-01-20 17:30:00', 0),
(6, 2, 3, '{"ops":[{"insert":"我认为理解线程安全很重要。"}]}', '2023-01-20 18:15:00', 5);

-- 插入交互记录
INSERT INTO `interact` (`tid`, `uid`, `type`, `time`) VALUES
(1, 2, 'like', '2023-01-10 12:00:00'),
(1, 3, 'like', '2023-01-10 14:00:00'),
(2, 1, 'like', '2023-01-15 15:30:00'),
(2, 3, 'like', '2023-01-15 16:15:00'),
(2, 1, 'collect', '2023-01-15 15:35:00'),
(3, 1, 'like', '2023-01-20 17:35:00'),
(3, 2, 'like', '2023-01-20 18:20:00'),
(3, 1, 'collect', '2023-01-20 17:40:00'); 