package com.example.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.entity.dto.Notification;
import com.example.entity.vo.response.NotificationVO;
import com.example.mapper.NotificationMapper;
import com.example.service.impl.NotificationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class NotificationServiceTest {

    @Mock
    private NotificationMapper notificationMapper;

    @InjectMocks
    private NotificationServiceImpl notificationService;

    private Notification testNotification;

    @BeforeEach
    void setUp() {
        testNotification = new Notification();
        testNotification.setId(1);
        testNotification.setUid(1);
        testNotification.setTitle("测试通知");
        testNotification.setContent("这是一条测试通知内容");
        testNotification.setType("comment");
        testNotification.setUrl("/topic/1");
        testNotification.setTime("2023-01-01 10:00:00");
    }

    @Test
    void testAddNotification() {
        // 准备测试数据
        ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);
        doReturn(true).when(notificationService).save(any(Notification.class));

        // 执行测试
        notificationService.addNotification(1, "测试通知", "这是一条测试通知内容", "comment", "/topic/1");

        // 验证结果
        verify(notificationService).save(notificationCaptor.capture());
        Notification captured = notificationCaptor.getValue();
        assertEquals(1, captured.getUid());
        assertEquals("测试通知", captured.getTitle());
        assertEquals("这是一条测试通知内容", captured.getContent());
        assertEquals("comment", captured.getType());
        assertEquals("/topic/1", captured.getUrl());
    }

    @Test
    void testDeleteUserNotification() {
        // 准备测试数据
        doReturn(true).when(notificationService).remove(any());

        // 执行测试
        notificationService.deleteUserNotification(1, 1);

        // 验证结果
        verify(notificationService).remove(any());
    }

    @Test
    void testDeleteUserAllNotification() {
        // 准备测试数据
        doReturn(true).when(notificationService).remove(any());

        // 执行测试
        notificationService.deleteUserAllNotification(1);

        // 验证结果
        verify(notificationService).remove(any());
    }

    @Test
    void testFindUserNotification() {
        // 准备测试数据
        List<Notification> notifications = Arrays.asList(testNotification);
        doReturn(notifications).when(notificationService).list(any());

        // 执行测试
        List<NotificationVO> result = notificationService.findUserNotification(1);

        // 验证结果
        verify(notificationService).list(any());
        // 注意：由于 asViewObject 方法在单元测试中无法模拟，我们只能验证 list 方法被调用
    }

    @Test
    void testDeleteNotification() {
        // 准备测试数据
        when(notificationMapper.deleteById(anyInt())).thenReturn(1);

        // 执行测试
        boolean result = notificationService.deleteNotification(1, 1);

        // 验证结果
        assertTrue(result);
        verify(notificationMapper).deleteById(1);
    }

    @Test
    void testDeleteNotification_NotFound() {
        // 准备测试数据
        when(notificationMapper.deleteById(anyInt())).thenReturn(0);

        // 执行测试
        boolean result = notificationService.deleteNotification(1, 1);

        // 验证结果
        assertFalse(result);
        verify(notificationMapper).deleteById(1);
    }

    @Test
    void testClearNotification() {
        // 准备测试数据
        when(notificationMapper.delete(any(QueryWrapper.class))).thenReturn(5);

        // 执行测试
        int result = notificationService.clearNotification(1);

        // 验证结果
        assertEquals(5, result);
        verify(notificationMapper).delete(any(QueryWrapper.class));
    }

    @Test
    void testListNotification() {
        // 准备测试数据
        Page<Notification> page = new Page<>();
        page.setRecords(Arrays.asList(testNotification));
        page.setTotal(1);
        when(notificationMapper.selectPage(any(Page.class), any(QueryWrapper.class))).thenReturn(page);

        // 执行测试
        Page<Notification> result = notificationService.listNotification(1, 1, 10);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getRecords().size());
        assertEquals(1, result.getTotal());
        assertEquals("测试通知", result.getRecords().get(0).getTitle());
    }

    @Test
    void testCountNotification() {
        // 准备测试数据
        when(notificationMapper.selectCount(any(QueryWrapper.class))).thenReturn(5L);

        // 执行测试
        long result = notificationService.countNotification(1);

        // 验证结果
        assertEquals(5L, result);
        verify(notificationMapper).selectCount(any(QueryWrapper.class));
    }
} 