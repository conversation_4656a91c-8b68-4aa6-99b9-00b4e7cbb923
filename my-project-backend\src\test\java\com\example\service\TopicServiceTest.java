package com.example.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.entity.dto.Account;
import com.example.entity.dto.Topic;
import com.example.entity.dto.TopicComment;
import com.example.entity.dto.TopicType;
import com.example.entity.vo.request.AddCommentVO;
import com.example.entity.vo.request.TopicCreateVO;
import com.example.entity.vo.request.TopicUpdateVO;
import com.example.entity.vo.response.CommentVO;
import com.example.entity.vo.response.TopicDetailVO;
import com.example.entity.vo.response.TopicPreviewVO;
import com.example.mapper.AccountDetailsMapper;
import com.example.mapper.AccountMapper;
import com.example.mapper.AccountPrivacyMapper;
import com.example.mapper.TopicCommentMapper;
import com.example.mapper.TopicMapper;
import com.example.mapper.TopicTypeMapper;
import com.example.service.impl.TopicServiceImpl;
import com.example.utils.CacheUtils;
import com.example.utils.FlowUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TopicServiceTest {

    @Mock
    private TopicMapper topicMapper;
    
    @Mock
    private TopicTypeMapper topicTypeMapper;
    
    @Mock
    private TopicCommentMapper commentMapper;
    
    @Mock
    private AccountMapper accountMapper;
    
    @Mock
    private AccountDetailsMapper accountDetailsMapper;
    
    @Mock
    private AccountPrivacyMapper accountPrivacyMapper;
    
    @Mock
    private CacheUtils cacheUtils;
    
    @Mock
    private FlowUtils flowUtils;
    
    @Mock
    private StringRedisTemplate template;
    
    @Mock
    private NotificationService notificationService;
    
    @InjectMocks
    private TopicServiceImpl topicService;
    
    private Topic testTopic;
    private TopicType testType;
    private Account testAccount;
    private TopicComment testComment;
    
    @BeforeEach
    void setUp() throws Exception {
        testTopic = new Topic();
        testTopic.setId(1);
        testTopic.setTitle("测试话题");
        testTopic.setContent("{\"ops\":[{\"insert\":\"测试内容\\n\"}]}");
        testTopic.setUid(1);
        testTopic.setType(1);
        testTopic.setTime(new Date());
        
        testType = new TopicType();
        testType.setId(1);
        testType.setName("测试类型");
        testType.setDesc("测试类型描述");
        testType.setColor("#FF5722");
        
        testAccount = new Account();
        testAccount.setId(1);
        testAccount.setUsername("test");
        testAccount.setEmail("<EMAIL>");
        
        testComment = new TopicComment();
        testComment.setId(1);
        testComment.setUid(2);
        testComment.setTid(1);
        testComment.setContent("{\"ops\":[{\"insert\":\"测试评论\\n\"}]}");
        testComment.setTime(new Date());
        testComment.setQuote(0);
        
        // 初始化 TopicService 中的 types 集合
        List<TopicType> types = Arrays.asList(testType);
        when(topicTypeMapper.selectList(any())).thenReturn(types);
        
        // 使用反射设置 types 字段
        Set<Integer> typeSet = new HashSet<>();
        typeSet.add(1);
        ReflectionTestUtils.setField(topicService, "types", typeSet);
    }
    
    @Test
    void testListTypes() {
        // 准备测试数据
        List<TopicType> types = Arrays.asList(testType);
        when(topicTypeMapper.selectList(any())).thenReturn(types);
        
        // 执行测试
        List<TopicType> result = topicService.listTypes();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("测试类型", result.get(0).getName());
    }
    
    @Test
    void testCreateTopic_Success() {
        // 准备测试数据
        TopicCreateVO createVO = new TopicCreateVO();
        createVO.setTitle("新话题");
        createVO.setContent(new JSONObject());
        createVO.setType(1);
        
        when(flowUtils.limitPeriodCounterCheck(anyString(), anyInt(), anyInt())).thenReturn(true);
        when(topicMapper.insert(any())).thenReturn(1);
        
        // 执行测试
        String result = topicService.createTopic(1, createVO);
        
        // 验证结果
        assertNull(result);
        verify(topicMapper).insert(any());
        verify(cacheUtils).deleteCachePattern(anyString());
    }
    
    @Test
    void testCreateTopic_FlowLimited() {
        // 准备测试数据
        TopicCreateVO createVO = new TopicCreateVO();
        createVO.setTitle("新话题");
        createVO.setContent(new JSONObject());
        createVO.setType(1);
        
        when(flowUtils.limitPeriodCounterCheck(anyString(), anyInt(), anyInt())).thenReturn(false);
        
        // 执行测试
        String result = topicService.createTopic(1, createVO);
        
        // 验证结果
        assertEquals("发文频繁，请稍后再试！", result);
        verify(topicMapper, never()).insert(any());
    }
    
    @Test
    void testCreateTopic_InvalidType() {
        // 准备测试数据
        TopicCreateVO createVO = new TopicCreateVO();
        createVO.setTitle("新话题");
        createVO.setContent(new JSONObject());
        createVO.setType(999); // 不存在的类型
        
        // 执行测试
        String result = topicService.createTopic(1, createVO);
        
        // 验证结果
        assertEquals("文章类型非法！", result);
        verify(topicMapper, never()).insert(any());
    }
    
    @Test
    void testUpdateTopic_Success() {
        // 准备测试数据
        TopicUpdateVO updateVO = new TopicUpdateVO();
        updateVO.setId(1);
        updateVO.setTitle("更新话题");
        updateVO.setContent(new JSONObject());
        updateVO.setType(1);
        
        when(topicMapper.update(any(), any())).thenReturn(1);
        
        // 执行测试
        String result = topicService.updateTopic(1, updateVO);
        
        // 验证结果
        assertNull(result);
        verify(topicMapper).update(any(), any());
    }
    
    @Test
    void testCreateComment_Success() {
        // 准备测试数据
        AddCommentVO commentVO = new AddCommentVO();
        commentVO.setTid(1);
        commentVO.setContent("{\"ops\":[{\"insert\":\"新评论\\n\"}]}");
        commentVO.setQuote(0);
        
        when(flowUtils.limitPeriodCounterCheck(anyString(), anyInt(), anyInt())).thenReturn(true);
        when(commentMapper.insert(any())).thenReturn(1);
        when(topicMapper.selectById(anyInt())).thenReturn(testTopic);
        when(accountMapper.selectById(anyInt())).thenReturn(testAccount);
        
        // 执行测试
        String result = topicService.createComment(2, commentVO);
        
        // 验证结果
        assertNull(result);
        verify(commentMapper).insert(any());
        verify(notificationService).addNotification(anyInt(), anyString(), anyString(), anyString(), anyString());
    }
    
    @Test
    void testCreateComment_FlowLimited() {
        // 准备测试数据
        AddCommentVO commentVO = new AddCommentVO();
        commentVO.setTid(1);
        commentVO.setContent("{\"ops\":[{\"insert\":\"新评论\\n\"}]}");
        
        when(flowUtils.limitPeriodCounterCheck(anyString(), anyInt(), anyInt())).thenReturn(false);
        
        // 执行测试
        String result = topicService.createComment(1, commentVO);
        
        // 验证结果
        assertEquals("发表评论频繁，请稍后再试！", result);
        verify(commentMapper, never()).insert(any());
    }
    
    @Test
    void testComments() {
        // 准备测试数据
        Page<TopicComment> page = new Page<>();
        page.setRecords(Arrays.asList(testComment));
        
        when(commentMapper.selectPage(any(), any())).thenAnswer(invocation -> {
            Page<TopicComment> pageArg = invocation.getArgument(0);
            pageArg.setRecords(Arrays.asList(testComment));
            return null;
        });
        
        // 执行测试
        List<CommentVO> result = topicService.comments(1, 1);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
    }
    
    @Test
    void testDeleteComment() {
        // 执行测试
        topicService.deleteComment(1, 1);
        
        // 验证结果
        verify(commentMapper).delete(any());
    }
    
    @Test
    void testGetTopic() {
        // 准备测试数据
        when(topicMapper.selectById(anyInt())).thenReturn(testTopic);
        when(commentMapper.selectCount(any())).thenReturn(5L);
        
        // 执行测试
        TopicDetailVO result = topicService.getTopic(1, 1);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("测试话题", result.getTitle());
        assertEquals(5, result.getComments());
    }
} 