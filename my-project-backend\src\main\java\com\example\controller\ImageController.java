package com.example.controller;

import com.example.entity.RestBean;
import com.example.service.ImageService;
import com.example.utils.Const;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 图片处理控制器
 * 处理用户上传的图片，包括：
 * - 普通图片上传（用于论坛内容）
 * - 用户头像上传
 * 上传的图片会保存到MinIO对象存储服务
 */
@Slf4j
@RestController
@RequestMapping("/api/image")
public class ImageController {

    /**
     * 图片服务，用于处理图片上传和获取
     */
    @Resource
    ImageService service;

    /**
     * 上传普通图片接口
     * 用于上传论坛帖子中使用的图片
     * 
     * @param file 上传的图片文件
     * @param id 用户ID（从请求属性中获取）
     * @param response HTTP响应对象
     * @return 包含图片URL的响应对象
     * @throws IOException 如果文件处理过程中发生I/O错误
     */
    @PostMapping("/cache")
    public RestBean<String> uploadImage(@RequestParam("file") MultipartFile file,
                                        @RequestAttribute(Const.ATTR_USER_ID) int id,
                                        HttpServletResponse response) throws IOException {
        // 检查文件大小是否超过限制（5MB）
        if(file.getSize() > 1024 * 1024 * 5)
            return RestBean.failure(400, "头像图片不能大于5MB");
            
        log.info("正在进行图片上传操作...");
        // 调用服务上传图片
        String url = service.uploadImage(file, id);
        
        if(url != null) {
            // 上传成功，返回图片URL
            log.info("图片上传成功，大小: " + file.getSize());
            return RestBean.success(url);
        } else {
            // 上传失败，设置HTTP状态码并返回错误信息
            response.setStatus(400);
            return RestBean.failure(400, "图片上传失败，请联系管理员！");
        }
    }

    /**
     * 上传用户头像接口
     * 用于更新用户的个人头像
     * 
     * @param file 上传的头像图片文件
     * @param id 用户ID（从请求属性中获取）
     * @return 包含头像URL的响应对象
     * @throws IOException 如果文件处理过程中发生I/O错误
     */
    @PostMapping("/avatar")
    public RestBean<String> uploadAvatar(@RequestParam("file") MultipartFile file,
                                         @RequestAttribute(Const.ATTR_USER_ID) int id) throws IOException {
        // 检查文件大小是否超过限制（100KB）
        if(file.getSize() > 1024 * 100)
            return RestBean.failure(400, "头像图片不能大于100KB");
            
        log.info("正在进行头像上传操作...");
        // 调用服务上传头像
        String url = service.uploadAvatar(file, id);
        
        if(url != null) {
            // 上传成功，返回头像URL
            log.info("头像上传成功，大小: " + file.getSize());
            return RestBean.success(url);
        } else {
            // 上传失败，返回错误信息
            return RestBean.failure(400, "头像上传失败，请联系管理员！");
        }
    }
}
