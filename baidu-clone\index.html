<!DOC<PERSON><PERSON>E html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度一下，你就知道</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAABILAAASCwAAAAAAAAAAAAD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///wAAAAA">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="top-nav">
        <div class="nav-container">
            <div class="nav-left">
                <a href="#" class="nav-link">新闻</a>
                <a href="#" class="nav-link">hao123</a>
                <a href="#" class="nav-link">地图</a>
                <a href="#" class="nav-link">贴吧</a>
                <a href="#" class="nav-link">学术</a>
                <a href="#" class="nav-link more-link">更多 ▼</a>
            </div>
            <div class="nav-right">
                <a href="#" class="nav-link">设置</a>
                <a href="#" class="nav-link login-btn">登录</a>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 百度Logo -->
        <div class="logo-container">
            <div class="logo">
                <svg width="270" height="129" viewBox="0 0 270 129" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M23.7 33.5H39.4C46.2 33.5 51.7 39.0 51.7 45.8V61.5C51.7 68.3 46.2 73.8 39.4 73.8H23.7V33.5Z" fill="#3385FF"/>
                    <path d="M70.1 33.5H85.8C92.6 33.5 98.1 39.0 98.1 45.8V61.5C98.1 68.3 92.6 73.8 85.8 73.8H70.1V33.5Z" fill="#3385FF"/>
                    <path d="M116.5 33.5H132.2C139.0 33.5 144.5 39.0 144.5 45.8V61.5C144.5 68.3 139.0 73.8 132.2 73.8H116.5V33.5Z" fill="#3385FF"/>
                    <path d="M162.9 33.5H178.6C185.4 33.5 190.9 39.0 190.9 45.8V61.5C190.9 68.3 185.4 73.8 178.6 73.8H162.9V33.5Z" fill="#3385FF"/>
                    <text x="23" y="100" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#3385FF">百度</text>
                </svg>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="search-container">
            <div class="search-box">
                <input type="text" class="search-input" placeholder="请输入搜索内容" autocomplete="off" id="searchInput">
                <button class="search-btn" type="button" onclick="performSearch()">
                    <svg class="search-icon" viewBox="0 0 24 24" fill="none">
                        <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            
            <!-- 搜索建议下拉框 -->
            <div class="search-suggestions" id="suggestions">
                <!-- 动态生成的搜索建议 -->
            </div>
        </div>

        <!-- 搜索按钮组 -->
        <div class="button-group">
            <button class="search-button primary" type="button" onclick="performSearch()">百度一下</button>
            <button class="search-button secondary" type="button" onclick="feelingLucky()">我很幸运</button>
        </div>

        <!-- 快捷导航 -->
        <div class="quick-nav">
            <a href="#" class="quick-link">
                <div class="quick-icon">📰</div>
                <span>新闻</span>
            </a>
            <a href="#" class="quick-link">
                <div class="quick-icon">🎵</div>
                <span>音乐</span>
            </a>
            <a href="#" class="quick-link">
                <div class="quick-icon">📺</div>
                <span>视频</span>
            </a>
            <a href="#" class="quick-link">
                <div class="quick-icon">🖼️</div>
                <span>图片</span>
            </a>
            <a href="#" class="quick-link">
                <div class="quick-icon">🗺️</div>
                <span>地图</span>
            </a>
            <a href="#" class="quick-link">
                <div class="quick-icon">📚</div>
                <span>百科</span>
            </a>
        </div>
    </main>

    <!-- 底部信息 -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="#" class="footer-link">关于百度</a>
                <a href="#" class="footer-link">About Baidu</a>
                <a href="#" class="footer-link">百度推广</a>
                <a href="#" class="footer-link">使用百度前必读</a>
                <a href="#" class="footer-link">意见反馈</a>
                <a href="#" class="footer-link">帮助中心</a>
            </div>
            <div class="footer-info">
                <p>©2024 Baidu 使用百度前必读 意见反馈 京ICP证030173号</p>
                <p>京公网安备11000002000001号</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
