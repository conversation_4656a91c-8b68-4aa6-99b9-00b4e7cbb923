package com.example.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web服务通用配置类
 * 负责配置和初始化Web应用所需的各种Bean，包括：
 * - 密码加密器
 * - RestTemplate客户端
 * - MyBatis Plus分页插件
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    /**
     * 密码加密器Bean
     * 使用BCrypt算法进行密码加密和验证
     * BCrypt是一种单向哈希算法，适用于密码存储
     * 
     * @return 配置好的BCrypt密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder(){
        return new BCryptPasswordEncoder();
    }

    /**
     * RestTemplate客户端Bean
     * 用于发送HTTP请求，主要用于调用外部API（如天气API）
     * 
     * @return 配置好的RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * MyBatis Plus拦截器Bean
     * 配置MyBatis Plus的分页插件，支持数据库分页查询
     * 用于优化大数据量查询的性能
     * 
     * @return 配置好的MyBatis Plus拦截器
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor(){
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return interceptor;
    }
}
