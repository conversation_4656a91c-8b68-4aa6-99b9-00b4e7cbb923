<script setup>
import {Edit<PERSON>en, User} from "@element-plus/icons-vue";
import {apiUserDetailTotal, apiUserList, apiUserSave} from "@/net/api/user";
import {reactive, watch, watchEffect} from "vue";
import {useStore} from "@/store";
import {get} from "@/net";
import {ElMessage} from "element-plus";
const store=useStore()
const userTable=reactive({
  page: 1,
  size: 10,
  total: 0,
  data: []
})
const editor=reactive({
  id:0,
  display:false,
  temp:{},
  loading:false
})
function userStatus(user){
    if(user.mute && user.banned)
        return '禁言中、封禁中'
    else if(user.mute)
        return '禁言中'
    else if(user.banned)
        return '封禁中'
    else
        return '正常'
}
function openUserEditor(user){
    editor.id=user.id
    editor.display=true
    editor.loading=true
  apiUserDetailTotal(editor.id,(data)=>{
    editor.temp={...data,...user}
    // {...data, ...user}：这会创建一个新对象，
    // 先将 data 对象的属性添加进去，接着再把 user
    // 对象的属性添加进去。要是 data 和 user 存在相同的属性名，
    // 那么 user 对象里的属性值会覆盖 data 对象里的同名属性值。
    editor.loading=false
  })
}

watchEffect(()=>apiUserList(userTable.page,userTable.size,(data)=>{
  userTable.total=data.total
  userTable.data=data.list
}))
// watchEffect 是一个副作用函数，它会立即执行传入的回调函数，并且会自动追踪回调函数中所使用的响应式数据。当这些响应式数据发生变化时，回调函数会再次执行。
function saveUserDetail(){

}
</script>

<template>
  <div class="user-admin">
    <div class="title">
        <el-icon><User/></el-icon>
        论坛用户列表
    </div>
    <div class="desc">
        在这里管理论坛的所有用户，包括账号信息、封禁和禁言处理。
    </div>
    <el-table :data="userTable.data" height="320">
      <el-table-column prop="id" label="编号" />
      <el-table-column prop="username" label="用户名">
        <template #default="{row}">
        <div class="table-username">
          <el-avatar :size="30" :src="store.avatarUserUrl(row.avatar)"/>
          <div>{{row.username}}</div>
        </div>
        </template>
      </el-table-column>
      <el-table-column label="角色">
        <template #default="{row}">
          <el-tag type="danger" v-if="row.role==='admin'">管理员</el-tag>
          <el-tag type="info" v-else>普通用户</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="email" label="电子邮箱" />
      <el-table-column label="注册时间">
        <template #default="{row}">
          <div>{{new Date(row.registerTime).toLocaleString()}}</div>
        </template>
      </el-table-column>
      <el-table-column label="状态"  align="center">
        <template #default="{row}">
          {{userStatus(row)}}
        </template>
      </el-table-column>
      <el-table-column label="操作"  align="center">
        <template #default="{row}">
          <el-button size="small"  type="primary" :icon="EditPen" @click="openUserEditor(row)" :disabled="row.role==='admin'">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination :total="userTable.total"
                      v-model:current-page="userTable.page"
                      v-model:page-size="userTable.size"
                      layout="total, sizes, prev, pager, next, jumper"/>
    </div>
    <el-drawer v-model="editor.display">
      <template #header>
        <div>
          <div style="font-weight: bold">
            <div><el-icon><EditPen/></el-icon>编辑用户信息</div>
          </div>
          <div style="font-size: 13px">编辑完成后请点击下方保存按钮</div>
        </div>
      </template>
      <el-form label-position="top" :model="editor.temp" :disabled="editor.loading">
        <el-form-item label="用户名">
            <el-input v-model="editor.temp.username" placeholder="用户名" :disabled="editor.loading"/>
        </el-form-item>
        <el-form-item label="电子邮件">
          <el-input v-model="editor.temp.email"  :disabled="editor.loading"/>
        </el-form-item>
        <div style="display: flex;font-size: 14px;gap: 20px">
          <div>
            <span style="margin-right: 10px">禁言</span>
            <el-switch v-model="editor.temp.mute"/>
          </div>
          <el-divider style="height: 30px" direction="vertical"/>
          <div>
            <span style="margin-right: 10px">账号封禁</span>
            <el-switch v-model="editor.temp.banned"/>
          </div>
        </div>
        <div style="margin-top: 10px;color: grey;font-size: 14px">
          注册时间:{{new Date(editor.temp.registerTime).toLocaleString()}}
        </div>
      </el-form>
      <el-divider/>
      <template #footer>
        <div style="text-align: center">
          <el-button type="success" @click="saveUserDetail">保存</el-button>
          <el-button type="info" @click="editor.display=false">取消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<style lang="less" scoped>
.user-admin{
    .title{
      font-weight: bold;
    }
    .desc{
      color: #999;
      font-size: 13px;
      margin-bottom: 20px;
    }
  .table-username{
    height: 30px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: right;
  }
  :deep(.el-drawer__header) {
    margin-bottom: 0;
  }
}
</style>