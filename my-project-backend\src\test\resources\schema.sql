-- 用户账户表
CREATE TABLE IF NOT EXISTS `account` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `role` varchar(255) NOT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `register_time` datetime NOT NULL,
  `banned` tinyint(1) NOT NULL DEFAULT '0',
  `top` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_name` (`username`),
  UNIQUE KEY `unique_email` (`email`)
);

-- 用户详细信息表
CREATE TABLE IF NOT EXISTS `account_details` (
  `id` int NOT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `qq` varchar(20) DEFAULT NULL,
  `wx` varchar(50) DEFAULT NULL,
  `desc` varchar(500) DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

-- 用户隐私设置表
CREATE TABLE IF NOT EXISTS `account_privacy` (
  `id` int NOT NULL,
  `phone` tinyint(1) NOT NULL DEFAULT '0',
  `email` tinyint(1) NOT NULL DEFAULT '0',
  `qq` tinyint(1) NOT NULL DEFAULT '0',
  `wx` tinyint(1) NOT NULL DEFAULT '0',
  `gender` tinyint(1) NOT NULL DEFAULT '0',
  `birthday` tinyint(1) NOT NULL DEFAULT '0',
  `location` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
);

-- 图片存储表
CREATE TABLE IF NOT EXISTS `image_store` (
  `id` varchar(255) NOT NULL,
  `uid` int NOT NULL,
  `upload_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
);

-- 通知表
CREATE TABLE IF NOT EXISTS `notification` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` varchar(1000) NOT NULL,
  `type` varchar(20) NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `time` datetime NOT NULL,
  PRIMARY KEY (`id`)
);

-- 话题表
CREATE TABLE IF NOT EXISTS `topic` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `uid` int NOT NULL,
  `type` int NOT NULL,
  `time` datetime NOT NULL,
  `top` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
);

-- 话题评论表
CREATE TABLE IF NOT EXISTS `topic_comment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL,
  `tid` int NOT NULL,
  `content` text NOT NULL,
  `time` datetime NOT NULL,
  `quote` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
);

-- 话题类型表
CREATE TABLE IF NOT EXISTS `topic_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `desc` varchar(255) DEFAULT NULL,
  `color` varchar(20) NOT NULL,
  PRIMARY KEY (`id`)
);

-- 交互记录表
CREATE TABLE IF NOT EXISTS `interact` (
  `tid` int NOT NULL,
  `uid` int NOT NULL,
  `type` varchar(10) NOT NULL,
  `time` datetime NOT NULL,
  PRIMARY KEY (`tid`,`uid`,`type`)
); 