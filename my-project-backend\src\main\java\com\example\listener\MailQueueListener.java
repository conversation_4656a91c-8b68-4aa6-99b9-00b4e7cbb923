package com.example.listener;

import jakarta.annotation.Resource;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 邮件队列监听器
 * 监听RabbitMQ中的邮件队列，异步处理邮件发送任务
 * 支持多种类型的邮件发送：注册验证、密码重置、邮箱修改等
 * 通过异步处理可以提高系统响应速度，避免邮件发送延迟影响用户体验
 */
@Component
@RabbitListener(queues = "mail")
public class MailQueueListener {

    /**
     * Spring邮件发送器
     * 用于实际发送邮件
     */
    @Resource
    JavaMailSender sender;

    /**
     * 邮件发送者地址
     * 从配置文件中注入
     */
    @Value("${spring.mail.username}")
    String username;

    /**
     * 处理邮件发送消息
     * 从队列中接收邮件发送请求，根据类型生成不同内容的邮件并发送
     * 
     * @param data 邮件信息数据，包含邮箱地址、验证码和邮件类型
     */
    @RabbitHandler
    public void sendMailMessage(Map<String, Object> data) {
        // 获取目标邮箱地址
        String email = data.get("email").toString();
        // 获取验证码
        Integer code = (Integer) data.get("code");
        // 根据邮件类型创建不同内容的邮件
        SimpleMailMessage message = switch (data.get("type").toString()) {
            // 注册验证邮件
            case "register" ->
                    createMessage("欢迎注册我们的网站",
                            "您的邮件注册验证码为: "+code+"，有效时间3分钟，为了保障您的账户安全，请勿向他人泄露验证码信息。",
                            email);
            // 密码重置邮件
            case "reset" ->
                    createMessage("您的密码重置邮件",
                            "你好，您正在执行重置密码操作，验证码: "+code+"，有效时间3分钟，如非本人操作，请无视。",
                            email);
            // 邮箱修改验证邮件
            case "modify" ->
                    createMessage("您的邮件修改验证邮件",
                            "您好，您正在绑定新的电子邮件地址，验证码: "+code+"，有效时间3分钟，如非本人操作，请无视。",
                            email);
            // 未知类型，返回null
            default -> null;
        };
        // 如果邮件为空则不发送
        if(message == null) return;
        // 发送邮件
        sender.send(message);
    }

    /**
     * 创建简单邮件消息
     * 封装邮件的标题、内容、收件人和发件人信息
     * 
     * @param title 邮件标题
     * @param content 邮件正文内容
     * @param email 收件人邮箱地址
     * @return 封装好的邮件消息对象
     */
    private SimpleMailMessage createMessage(String title, String content, String email){
        SimpleMailMessage message = new SimpleMailMessage();
        message.setSubject(title);      // 设置邮件标题
        message.setText(content);       // 设置邮件内容
        message.setTo(email);           // 设置收件人
        message.setFrom(username);      // 设置发件人
        return message;
    }
}
