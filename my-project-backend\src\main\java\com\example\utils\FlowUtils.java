package com.example.utils;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 流量控制工具类
 * 用于针对不同场景进行限流操作，支持多种限流策略：
 * 1. 单次请求冷却时间限制
 * 2. 阶段性请求次数限制
 * 3. 超限后的封禁升级机制
 * 
 * 基于Redis实现，通过计数器和时间周期进行流量控制
 */
@Slf4j
@Component
public class FlowUtils {

    /**
     * Redis模板，用于存储计数器和封禁标记
     */
    @Resource
    StringRedisTemplate template;

    /**
     * 默认限制行为策略
     * 当请求超出限制时返回false，未超出时返回true
     */
    private static final LimitAction defaultAction = overclock -> !overclock;

    /**
     * 单次请求冷却时间限制
     * 适用场景：某些操作需要冷却时间，如发送验证码
     * 例如：3秒内不能重复发送验证码请求
     * 
     * @param key Redis中存储的键名，通常包含用户标识或IP地址
     * @param blockTime 冷却时间（秒）
     * @return 是否通过限流检查，true表示允许请求，false表示请求被限制
     */
    public boolean limitOnceCheck(String key, int blockTime){
        return this.internalCheck(key, 1, blockTime, defaultAction);
    }

    /**
     * 单次请求冷却时间限制，带升级机制
     * 如果用户在冷却时间内继续发起请求，将延长冷却时间
     * 
     * @param key Redis中存储的键名，通常包含用户标识或IP地址
     * @param frequency 允许的请求次数，超过此次数将触发升级限制
     * @param baseTime 基础冷却时间（秒）
     * @param upgradeTime 升级后的冷却时间（秒），一般大于baseTime
     * @return 是否通过限流检查，true表示允许请求，false表示请求被限制
     */
    public boolean limitOnceUpgradeCheck(String key, int frequency, int baseTime, int upgradeTime){
        return this.internalCheck(key, frequency, baseTime, (overclock) -> {
                    if (overclock)
                        // 如果超出限制次数，设置更长的冷却时间
                        template.opsForValue().set(key, "1", upgradeTime, TimeUnit.SECONDS);
                    return false;
                });
    }

    /**
     * 周期内请求次数限制，超出后封禁
     * 适用场景：防止接口被短时间内大量调用，如登录接口
     * 例如：3秒内最多请求20次，超出后封禁5分钟
     * 
     * @param counterKey 计数键，用于记录请求次数
     * @param blockKey 封禁键，用于标记用户是否被封禁
     * @param blockTime 封禁时间（秒）
     * @param frequency 允许的最大请求次数
     * @param period 计数周期（秒）
     * @return 是否通过限流检查，true表示允许请求，false表示请求被限制
     */
    public boolean limitPeriodCheck(String counterKey, String blockKey, int blockTime, int frequency, int period){
        return this.internalCheck(counterKey, frequency, period, (overclock) -> {
                    if (overclock)
                        // 如果超出限制次数，设置封禁标记
                        template.opsForValue().set(blockKey, "", blockTime, TimeUnit.SECONDS);
                    return !overclock;
                });
    }

    /**
     * 周期内请求次数限制，不带封禁
     * 适用场景：控制接口调用频率，但不需要封禁
     * 例如：限制用户1分钟内只能提交10次评论
     * 
     * @param counterKey 计数键，用于记录请求次数
     * @param frequency 允许的最大请求次数
     * @param period 计数周期（秒）
     * @return 是否通过限流检查，true表示允许请求，false表示请求被限制
     */
    public boolean limitPeriodCounterCheck(String counterKey, int frequency, int period){
        return this.internalCheck(counterKey, frequency, period, defaultAction);
    }

    /**
     * 内部限流检查核心逻辑
     * 通过Redis实现计数器功能，并根据指定的策略执行限制行为
     * 
     * @param key Redis中存储的键名
     * @param frequency 允许的最大请求次数
     * @param period 计数周期（秒）
     * @param action 限制行为策略，定义超限后的处理逻辑
     * @return 是否通过限流检查
     */
    private boolean internalCheck(String key, int frequency, int period, LimitAction action){
        if (Boolean.TRUE.equals(template.hasKey(key))) {
            // 键已存在，增加计数器值
            Long value = Optional.ofNullable(template.opsForValue().increment(key)).orElse(0L);
            // 根据计数值和策略决定是否限制请求
            return action.run(value > frequency);
        } else {
            // 键不存在，创建计数器，初始值为1，有效期为指定的周期
            template.opsForValue().set(key, "1", period, TimeUnit.SECONDS);
            // 首次请求，直接通过
            return true;
        }
    }

    /**
     * 限制行为策略接口
     * 用于定义不同场景下超出限制后的处理逻辑
     */
    private interface LimitAction {
        /**
         * 执行限制策略
         * 
         * @param overclock 是否超出限制次数
         * @return 是否通过请求，true表示允许，false表示拒绝
         */
        boolean run(boolean overclock);
    }
}
