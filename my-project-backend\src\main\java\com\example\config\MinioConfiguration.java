package com.example.config;

import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MinIO对象存储配置类
 * 负责配置和初始化MinIO客户端，用于图片存储和获取
 * MinIO是一个高性能的分布式对象存储服务，兼容Amazon S3云存储服务接口
 */
@Slf4j
@Configuration
public class MinioConfiguration {

    /**
     * MinIO服务器端点地址
     * 从配置文件中注入
     */
    @Value("${spring.minio.endpoint}")
    String endpoint;
    
    /**
     * MinIO访问用户名
     * 从配置文件中注入
     */
    @Value("${spring.minio.username}")
    String username;
    
    /**
     * MinIO访问密码
     * 从配置文件中注入
     */
    @Value("${spring.minio.password}")
    String password;

    /**
     * 创建并配置MinIO客户端Bean
     * 设置服务端点和认证信息
     * 
     * @return 配置好的MinIO客户端实例
     */
    @Bean
    public MinioClient minioClient(){
        log.info("初始化MinIO客户端...");
        return MinioClient.builder()
                .endpoint(endpoint)  // 设置MinIO服务端点
                .credentials(username, password)  // 设置访问凭证
                .build();
    }
}
