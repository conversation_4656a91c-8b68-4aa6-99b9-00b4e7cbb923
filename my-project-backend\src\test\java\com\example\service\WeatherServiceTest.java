package com.example.service;

import com.alibaba.fastjson2.JSONObject;
import com.example.entity.vo.response.WeatherVO;
import com.example.service.impl.WeatherServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class WeatherServiceTest {

    @Mock
    private RestTemplate restTemplate;
    
    @Mock
    private StringRedisTemplate stringRedisTemplate;
    
    @Mock
    private ValueOperations<String, String> valueOperations;
    
    @InjectMocks
    private WeatherServiceImpl weatherService;
    
    private JSONObject mockWeatherData;
    
    @BeforeEach
    void setUp() {
        // 模拟 Redis 操作
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        
        // 创建模拟的天气数据
        mockWeatherData = new JSONObject();
        JSONObject now = new JSONObject();
        now.put("temp", "25");
        now.put("text", "晴");
        now.put("windDir", "东南风");
        now.put("humidity", "40");
        mockWeatherData.put("now", now);
        
        JSONObject location = new JSONObject();
        location.put("name", "北京");
        mockWeatherData.put("location", location);
    }
    
    @Test
    void testGetWeather_FromCache() {
        // 准备测试数据 - 从缓存获取
        when(valueOperations.get(anyString())).thenReturn(mockWeatherData.toJSONString());
        
        // 执行测试
        WeatherVO result = weatherService.getWeather("北京");
        
        // 验证结果
        assertNotNull(result);
        assertEquals("北京", result.getCity());
        assertEquals("25°C", result.getTemp());
        assertEquals("晴", result.getText());
        assertEquals("东南风", result.getWindDir());
        assertEquals("40%", result.getHumidity());
        
        // 验证调用
        verify(valueOperations).get(anyString());
        verify(restTemplate, never()).getForObject(anyString(), eq(String.class));
    }
    
    @Test
    void testGetWeather_FromAPI() {
        // 准备测试数据 - 缓存未命中，从API获取
        when(valueOperations.get(anyString())).thenReturn(null);
        when(restTemplate.getForObject(anyString(), eq(String.class))).thenReturn(mockWeatherData.toJSONString());
        
        // 执行测试
        WeatherVO result = weatherService.getWeather("北京");
        
        // 验证结果
        assertNotNull(result);
        assertEquals("北京", result.getCity());
        assertEquals("25°C", result.getTemp());
        assertEquals("晴", result.getText());
        assertEquals("东南风", result.getWindDir());
        assertEquals("40%", result.getHumidity());
        
        // 验证调用
        verify(valueOperations).get(anyString());
        verify(restTemplate).getForObject(anyString(), eq(String.class));
        verify(valueOperations).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
    }
    
    @Test
    void testGetWeather_APIFailure() {
        // 准备测试数据 - 缓存未命中，API调用失败
        when(valueOperations.get(anyString())).thenReturn(null);
        when(restTemplate.getForObject(anyString(), eq(String.class))).thenReturn(null);
        
        // 执行测试
        WeatherVO result = weatherService.getWeather("北京");
        
        // 验证结果
        assertNull(result);
        
        // 验证调用
        verify(valueOperations).get(anyString());
        verify(restTemplate).getForObject(anyString(), eq(String.class));
        verify(valueOperations, never()).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
    }
} 